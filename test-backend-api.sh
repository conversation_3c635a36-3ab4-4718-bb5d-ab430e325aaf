#!/bin/bash

# FaceTrace Backend API Testing Script
# Tests the deployed API endpoints to ensure they're working correctly

set -e

PROJECT_ID="gold-braid-458901-v2"
SERVICE_NAME="facetrace-backend-api"
REGION="us-central1"

# Get the service URL
SERVICE_URL=$(gcloud run services describe $SERVICE_NAME --region $REGION --format 'value(status.url)')

echo "🧪 Testing FaceTrace Backend API at: $SERVICE_URL"
echo ""

# Test 1: Health check
echo "🔍 Test 1: Health check..."
curl -s "$SERVICE_URL/api/health" | jq '.' || echo "Health endpoint not available"
echo ""

# Test 2: Core API - Faces count
echo "🔍 Test 2: Core API - Faces count..."
curl -s "$SERVICE_URL/api/core?action=faces" | jq '.' || echo "Core API not available"
echo ""

# Test 3: Data API - Basic functionality
echo "🔍 Test 3: Data API - Basic functionality..."
curl -s "$SERVICE_URL/api/data?action=test" | jq '.' || echo "Data API not available"
echo ""

# Test 4: Search API - Status check
echo "🔍 Test 4: Search API - Status check..."
curl -s -X POST "$SERVICE_URL/api/search" \
  -H "Content-Type: application/json" \
  -d '{"action": "status"}' | jq '.' || echo "Search API not available"
echo ""

# Test 5: CORS headers
echo "🔍 Test 5: CORS headers check..."
curl -s -I "$SERVICE_URL/api/health" | grep -i "access-control" || echo "CORS headers not found"
echo ""

# Test 6: Response time check
echo "🔍 Test 6: Response time check..."
time curl -s "$SERVICE_URL/api/health" > /dev/null
echo ""

echo "✅ API testing completed!"
echo ""
echo "📊 Service Information:"
echo "Service URL: $SERVICE_URL"
echo "Region: $REGION"
echo "Project: $PROJECT_ID"
echo ""
echo "📝 Available endpoints:"
echo "- GET  $SERVICE_URL/api/health"
echo "- GET  $SERVICE_URL/api/core?action=faces"
echo "- POST $SERVICE_URL/api/search"
echo "- POST $SERVICE_URL/api/data"
