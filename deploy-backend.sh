#!/bin/bash

# FaceTrace Backend API Deployment Script for Google Cloud Run
# This script deploys only the backend API components to Google Cloud

set -e

# Configuration
PROJECT_ID="gold-braid-458901-v2"
SERVICE_NAME="facetrace-backend-api"
REGION="us-central1"
IMAGE_NAME="gcr.io/${PROJECT_ID}/${SERVICE_NAME}"

echo "🚀 Starting FaceTrace Backend API deployment to Google Cloud Run..."

# Step 1: Authenticate and set project
echo "📋 Step 1: Setting up Google Cloud authentication..."
gcloud auth login
gcloud config set project $PROJECT_ID
gcloud services enable run.googleapis.com
gcloud services enable cloudbuild.googleapis.com
gcloud services enable containerregistry.googleapis.com

# Step 2: Build and push Docker image
echo "🐳 Step 2: Building Docker image for backend API..."
docker build -t $IMAGE_NAME -f Dockerfile.backend .
docker push $IMAGE_NAME

# Step 3: Deploy to Cloud Run
echo "☁️ Step 3: Deploying to Cloud Run..."
gcloud run deploy $SERVICE_NAME \
  --image $IMAGE_NAME \
  --platform managed \
  --region $REGION \
  --allow-unauthenticated \
  --memory 2Gi \
  --cpu 2 \
  --timeout 3600 \
  --concurrency 1000 \
  --max-instances 10 \
  --set-env-vars NODE_ENV=production \
  --set-env-vars NEXT_PUBLIC_DISABLE_AUTH=true \
  --set-env-vars NEXT_PUBLIC_DISABLE_PAYMENT=true

echo "✅ Deployment completed!"
echo "🌐 Your backend API is available at:"
SERVICE_URL=$(gcloud run services describe $SERVICE_NAME --region $REGION --format 'value(status.url)')
echo $SERVICE_URL

echo ""
echo "📝 Next steps:"
echo "1. Run: chmod +x setup-env-vars.sh && ./setup-env-vars.sh"
echo "2. Run: chmod +x test-backend-api.sh && ./test-backend-api.sh"
echo "3. Configure your frontend to use: $SERVICE_URL"
echo "4. Set up custom domain (optional)"
echo ""
echo "🔧 Important: Update your frontend environment variables:"
echo "NEXT_PUBLIC_API_BASE_URL=$SERVICE_URL"
