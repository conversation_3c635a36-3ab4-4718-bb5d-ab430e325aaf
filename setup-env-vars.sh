#!/bin/bash

# FaceTrace Backend API Environment Variables Setup
# Run this script after the initial deployment to configure environment variables

set -e

PROJECT_ID="gold-braid-458901-v2"
SERVICE_NAME="facetrace-backend-api"
REGION="us-central1"

echo "🔧 Setting up environment variables for FaceTrace Backend API..."

# Core application settings
echo "📋 Setting core application variables..."
gcloud run services update $SERVICE_NAME \
  --region $REGION \
  --set-env-vars NODE_ENV=production \
  --set-env-vars NEXT_PUBLIC_DISABLE_AUTH=true \
  --set-env-vars NEXT_PUBLIC_DISABLE_PAYMENT=true

# Database configuration (replace with your actual database URL)
echo "🗄️ Setting database configuration..."
echo "⚠️  Please replace DATABASE_URL with your actual database connection string"
# gcloud run services update $SERVICE_NAME \
#   --region $REGION \
#   --set-env-vars DATABASE_URL="your-database-connection-string"

# reCAPTCHA configuration
echo "🔐 Setting reCAPTCHA configuration..."
echo "⚠️  Please replace with your actual reCAPTCHA keys"
# gcloud run services update $SERVICE_NAME \
#   --region $REGION \
#   --set-env-vars NEXT_PUBLIC_RECAPTCHA_SITE_KEY="6Lc6l1UrAAAAADAs8XyJsTjfsaT57xIRrIiKY7ls" \
#   --set-env-vars RECAPTCHA_SECRET_KEY="6Lc6l1UrAAAAANx7wMGesViZ7iSopz9POkEPubw9"

# External API configurations
echo "🌐 Setting external API configurations..."
echo "⚠️  Please configure your external API keys"
# gcloud run services update $SERVICE_NAME \
#   --region $REGION \
#   --set-env-vars SEARCH_API_KEY="your-search-api-key" \
#   --set-env-vars SEARCH_API_URL="your-search-api-url"

# Logging and monitoring
echo "📊 Setting logging configuration..."
gcloud run services update $SERVICE_NAME \
  --region $REGION \
  --set-env-vars LOG_LEVEL=info

echo "✅ Environment variables setup completed!"
echo ""
echo "🔧 Manual configuration required:"
echo "1. Set DATABASE_URL with your database connection string"
echo "2. Configure reCAPTCHA keys if needed"
echo "3. Set up external API keys for search functionality"
echo "4. Configure any additional secrets using Google Secret Manager"
echo ""
echo "📝 To set a variable manually, use:"
echo "gcloud run services update $SERVICE_NAME --region $REGION --set-env-vars KEY=VALUE"
