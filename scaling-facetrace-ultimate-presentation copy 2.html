<!DOCTYPE html>
1. Slide 1: Make Gov -> GOV
2. Slide 3: <PERSON><PERSON><PERSON>
Modify Content to add underneath the quote from slide 9. 
3. Next delete slide 9.
4. Now we have Data Puzzle.
Lets reduce this to 
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Scaling FaceTrace - Who's Behind The Face?</title>
    <meta name="description" content="Comprehensive presentation on FaceTrace scaling strategy">
    <meta name="author" content="Bryce Bayens">
    
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Electrolize:wght@400&display=swap" rel="stylesheet">

    <style>
        :root {
            --primary: #3B82F6;
            --primary-dark: #1E40AF;
            --primary-darker: #1E3A8A;
            --secondary: #ff6b6b;
            --accent: #60A5FA;
            --purple: #9b59b6;
            --orange: #f39c12;
            --green: #27ae60;
            --dark: #0a0a0a;
            --dark-light: #1a1a1a;
            --dark-lighter: #2a2a2a;
            --text: #FFFFFF;
            --text-bright: #F8FAFC;
            --text-dim: #CBD5E1;
            --text-muted: #94A3B8;
            --gradient-1: linear-gradient(135deg, #3B82F6, #1E40AF);
            --gradient-2: linear-gradient(135deg, #60A5FA, #3B82F6);
            --gradient-3: linear-gradient(135deg, #1E40AF, #1E3A8A);
            --shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
            --shadow-lg: 0 20px 50px rgba(0, 0, 0, 0.7);
            --blue-glow: 0 0 20px rgba(59, 130, 246, 0.3);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        html, body {
            height: 100%;
            overflow: hidden;
        }

        body {
            font-family: 'Electrolize', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
            background-color: var(--dark);
            color: var(--text);
            line-height: 1.6;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        /* Animated Background */
        .bg-animation {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
            background: var(--dark);
            overflow: hidden;
        }

        .bg-animation::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, var(--primary) 0%, transparent 70%);
            opacity: 0.08;
            animation: rotate 30s linear infinite;
        }

        @keyframes rotate {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        /* Unified Navigation and Progress Bar */
        .progress-container {
            position: fixed;
            top: 10px;
            left: 50%;
            transform: translateX(-50%);
            width: 90%;
            max-width: 800px;
            z-index: 9999;
            background: rgba(26, 26, 26, 0.98);
            backdrop-filter: blur(15px);
            border-radius: 20px;
            padding: 10px 15px;
            box-shadow: var(--shadow-lg);
            border: 1px solid rgba(59, 130, 246, 0.2);
            min-height: 80px;
        }

        .progress-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 6px;
        }

        .progress-bar-bg {
            width: 100%;
            height: 6px;
            background: var(--dark-lighter);
            border-radius: 3px;
            overflow: hidden;
            position: relative;
        }

        .progress-bar {
            width: 0%;
            height: 100%;
            background: var(--gradient-1);
            transition: width 0.3s ease;
            position: relative;
        }

        /* Progress indicator animation */
        .progress-bar::after {
            content: '●';
            position: absolute;
            right: -10px;
            top: -8px;
            font-size: 16px;
            color: var(--primary);
            animation: puzzlePulse 2s ease-in-out infinite;
        }

        @keyframes puzzlePulse {
            0%, 100% { transform: scale(1); opacity: 0.8; }
            50% { transform: scale(1.2); opacity: 1; }
        }

        .page-number {
            color: var(--primary);
            font-weight: 600;
            font-size: 0.9rem;
        }

        /* Integrated Navigation Bar */
        .nav-bar {
            display: flex;
            justify-content: center;
            gap: 8px;
            align-items: center;
            margin-top: 6px;
            padding-top: 6px;
            border-top: 1px solid rgba(59, 130, 246, 0.2);
            flex-wrap: wrap;
        }

        /* Ensure slide content stays below navigation */
        .slide {
            padding-top: 120px;
            padding-bottom: 40px;
        }

        .nav-item {
            color: var(--text-dim);
            font-size: 0.75rem;
            cursor: pointer;
            transition: all 0.3s ease;
            padding: 3px 6px;
            border-radius: 6px;
            font-family: 'Electrolize', monospace;
            white-space: nowrap;
        }

        .nav-item:hover {
            color: var(--primary);
            background: rgba(59, 130, 246, 0.1);
            transform: translateY(-2px);
        }

        .nav-item.active {
            color: var(--primary);
            background: rgba(59, 130, 246, 0.2);
            border: 1px solid rgba(59, 130, 246, 0.3);
        }

        /* Slide Container */
        .presentation-container {
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
        }

        .slide {
            position: absolute;
            width: 90%;
            max-width: 1200px;
            height: 90%;
            display: none;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 2rem;
            text-align: center;
        }

        .slide.active {
            display: flex;
        }

        /* Enhanced Global Fade Animations */
        .fade-element {
            opacity: 0;
            transform: translateY(30px);
            transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .fade-element.visible {
            opacity: 1;
            transform: translateY(0);
        }

        /* Staggered animation delays for multiple elements */
        .fade-element:nth-child(1) { transition-delay: 0.1s; }
        .fade-element:nth-child(2) { transition-delay: 0.3s; }
        .fade-element:nth-child(3) { transition-delay: 0.5s; }
        .fade-element:nth-child(4) { transition-delay: 0.7s; }
        .fade-element:nth-child(5) { transition-delay: 0.9s; }

        /* Slide-wide fade animation */
        .slide {
            opacity: 0;
            transition: opacity 0.6s ease-in-out;
        }

        .slide.active {
            opacity: 1;
        }

        /* Complete content fade wrapper */
        .slide-content-wrapper {
            opacity: 0;
            transform: translateY(20px);
            transition: all 0.8s ease;
        }

        .slide.active .slide-content-wrapper {
            opacity: 1;
            transform: translateY(0);
        }

        /* Enhanced Cover Page Layout */
        .cover-page-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            gap: 3rem;
            width: 100%;
            height: 100%;
        }

        .cover-page-container-enhanced {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            gap: 1.5rem;
            width: 100%;
            height: 100%;
            padding-top: 2%;
        }

        .main-logo-section {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 1rem;
        }

        .logo-section {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 2rem;
        }

        .logo-container-with-svg {
            display: flex;
            align-items: center;
            gap: 2rem;
            margin-bottom: 1rem;
        }

        .logo-container-with-svg-centered {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 1.5rem;
            margin-bottom: 0.5rem;
        }

        /* SVG Logo Styles */
        .svg-logo-wrapper {
            position: relative;
            width: 120px;
            height: 120px;
        }

        .svg-logo-wrapper-centered {
            position: relative;
            width: 100px;
            height: 100px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .svg-logo-wrapper::before,
        .svg-logo-wrapper-centered::before {
            content: '';
            position: absolute;
            inset: -10px;
            background: linear-gradient(135deg, #3B82F6, #1E40AF);
            border-radius: 50%;
            z-index: -1;
            animation: logoGlow 3s ease-in-out infinite alternate;
        }

        .facetrace-svg-logo,
        .facetrace-svg-logo-centered {
            width: 100%;
            height: 100%;
            filter: drop-shadow(0 0 20px rgba(59, 130, 246, 0.5));
            animation: logoFloat 4s ease-in-out infinite;
        }

        .facetrace-svg-logo-centered {
            transform: translate(0, 0); /* Ensure perfect centering */
        }

        .scanner-line {
            animation: scannerMove 2.5s ease-in-out infinite;
            transform-origin: 50px 50px;
        }

        @keyframes logoGlow {
            0% { box-shadow: 0 0 20px rgba(59, 130, 246, 0.3); }
            100% { box-shadow: 0 0 40px rgba(59, 130, 246, 0.6); }
        }

        @keyframes logoFloat {
            0%, 100% { transform: translateY(0px) scale(1); }
            50% { transform: translateY(-10px) scale(1.02); }
        }

        @keyframes scannerMove {
            0% { transform: translateY(0px); opacity: 0.3; }
            25% { transform: translateY(20px); opacity: 0.8; }
            50% { transform: translateY(0px); opacity: 0.3; }
            75% { transform: translateY(-20px); opacity: 0.8; }
            100% { transform: translateY(0px); opacity: 0.3; }
        }

        /* Enhanced Text Logo */
        .text-logo-container {
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            gap: 0.5rem;
        }

        .facetrace-logo-enhanced {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-family: 'Electrolize', 'SF Mono', monospace;
        }

        .facetrace-text {
            font-size: 3rem;
            font-weight: 700;
            background: linear-gradient(135deg, #3B82F6 0%, #1E40AF 50%, #1E3A8A 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            letter-spacing: 2px;
            animation: textShimmer 3s ease-in-out infinite;
        }

        .animated-dot {
            width: 8px;
            height: 8px;
            background: #3B82F6;
            border-radius: 50%;
            animation: dotPulse 2s ease-in-out infinite;
        }

        .pro-text {
            font-size: 2rem;
            font-weight: 600;
            background: linear-gradient(135deg, #3B82F6, #ff6b6b);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-family: 'Electrolize', 'SF Mono', monospace;
        }

        .enterprise-subtitle {
            font-size: 1.2rem;
            color: #94A3B8;
            font-weight: 500;
            font-family: 'Electrolize', 'SF Mono', monospace;
            letter-spacing: 1px;
            margin-left: 0.5rem;
        }

        @keyframes textShimmer {
            0%, 100% { filter: brightness(1); }
            50% { filter: brightness(1.2); }
        }

        @keyframes dotPulse {
            0%, 100% { opacity: 1; transform: scale(1); }
            50% { opacity: 0.3; transform: scale(1.3); }
        }

        /* Author Section */
        .author-section {
            text-align: center;
        }

        /* Legacy FaceTrace Logo (for other slides) */
        .facetrace-logo {
            font-size: 5rem;
            font-weight: 900;
            background: linear-gradient(135deg, #3B82F6 0%, #1E40AF 50%, #1E3A8A 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-transform: uppercase;
            letter-spacing: 3px;
            position: relative;
            margin-bottom: 1rem;
            font-family: 'Electrolize', 'SF Mono', monospace;
        }

        .logo-subtitle {
            font-size: 1.5rem;
            color: var(--text-dim);
            margin-bottom: 3rem;
            font-family: 'Electrolize', monospace;
            font-weight: 400;
        }

        .logo-subtitle-enhanced {
            font-size: 1.5rem;
            color: var(--text-dim);
            font-family: 'Electrolize', monospace;
            font-weight: 400;
            text-align: center;
            letter-spacing: 1px;
            min-height: 2rem;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        /* Typewriter Effect */
        #typewriter-text {
            display: inline-block;
        }

        .typewriter-cursor {
            display: inline-block;
            color: var(--primary);
            animation: blink 1s infinite;
            font-weight: 400;
            margin-left: 2px;
        }

        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0; }
        }

        .subtitle-section {
            margin: 1rem 0;
        }

        .vision-section {
            margin: 1.5rem 0;
        }

        .vision-statement {
            font-size: 2rem;
            font-weight: 900;
            background: linear-gradient(135deg, #3B82F6 0%, #1E40AF 50%, #1E3A8A 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-family: 'Electrolize', monospace;
            text-align: center;
            letter-spacing: 2px;
            text-transform: uppercase;
        }



        .logo-variation {
            font-size: 3rem;
            font-weight: 900;
            margin: 2rem 0;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            font-family: 'Electrolize', monospace;
        }

        .logo-main {
            background: linear-gradient(135deg, #3B82F6 0%, #1E40AF 50%, #1E3A8A 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .logo-gov {
            color: var(--secondary);
            font-weight: 700;
        }

        .enterprise-subtitle-main {
            font-size: 1.2rem;
            color: var(--text-dim);
            font-weight: 500;
            font-family: 'Electrolize', monospace;
            letter-spacing: 1px;
        }

        .categories-title {
            font-size: 1.8rem;
            color: var(--accent);
            font-weight: 600;
            margin: 2rem 0 3rem 0;
            font-family: 'Electrolize', monospace;
        }

        .sub-logos {
            display: flex;
            gap: 4rem;
            margin: 2rem 0;
            justify-content: center;
            flex-wrap: wrap;
        }

        .sub-logo-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 0.8rem;
            padding: 2rem;
            background: rgba(59, 130, 246, 0.05);
            border: 2px solid rgba(59, 130, 246, 0.2);
            border-radius: 20px;
            transition: all 0.3s ease;
            min-width: 250px;
        }

        .sub-logo-item:hover {
            background: rgba(59, 130, 246, 0.1);
            border-color: var(--primary);
            transform: translateY(-5px);
            box-shadow: var(--blue-glow);
        }

        .category-label {
            font-size: 0.9rem;
            color: var(--accent);
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
            font-family: 'Electrolize', monospace;
        }

        .sub-logo-number {
            font-size: 1.2rem;
            color: var(--primary);
            font-weight: 700;
            font-family: 'Electrolize', monospace;
        }

        .sub-logo-name {
            font-size: 1.8rem;
            font-weight: 700;
            font-family: 'Electrolize', monospace;
        }

        .sub-logo-facetrace {
            background: linear-gradient(135deg, #3B82F6 0%, #1E40AF 50%, #1E3A8A 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .sub-logo-suffix {
            color: var(--text);
        }

        .category-description {
            font-size: 0.9rem;
            color: var(--text-dim);
            text-align: center;
            font-family: 'Electrolize', monospace;
            font-weight: 400;
        }

        /* Enhanced Government Solutions Cards */
        .gov-solutions-header {
            text-align: center;
            margin-bottom: 4rem;
        }

        .main-header {
            font-size: 4rem;
            font-weight: 900;
            font-family: 'Electrolize', monospace;
            margin-bottom: 1rem;
        }

        .main-header {
            background: linear-gradient(135deg, #3B82F6 0%, #1E40AF 50%, #1E3A8A 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .enterprise-subtitle-header {
            font-size: 1.5rem;
            color: #E2E8F0;
            font-weight: 500;
            font-family: 'Electrolize', monospace;
            letter-spacing: 1px;
            margin-bottom: 2rem;
        }

        .section-title {
            font-size: 2rem;
            color: #60A5FA;
            font-weight: 600;
            font-family: 'Electrolize', monospace;
            letter-spacing: 1px;
        }

        .enhanced-cards-container {
            display: flex;
            justify-content: center;
            gap: 3rem;
            margin: 3rem 0;
            flex-wrap: wrap;
        }

        .enhanced-gov-card {
            background: linear-gradient(145deg, #1a1a1a, #2a2a2a);
            border: 2px solid #3B82F6;
            border-radius: 20px;
            padding: 3rem 2rem;
            min-width: 280px;
            max-width: 320px;
            text-align: center;
            transition: all 0.4s ease;
            position: relative;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        }

        .enhanced-gov-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.1), transparent);
            transition: left 0.6s ease;
        }

        .enhanced-gov-card:hover::before {
            left: 100%;
        }

        .enhanced-gov-card:hover {
            transform: translateY(-10px) scale(1.02);
            border-color: #60A5FA;
            box-shadow: 0 20px 50px rgba(59, 130, 246, 0.3);
        }

        .card-level-indicator {
            font-size: 1rem;
            color: #60A5FA;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 2px;
            font-family: 'Electrolize', monospace;
            margin-bottom: 1rem;
        }

        .card-number {
            font-size: 2.5rem;
            color: #3B82F6;
            font-weight: 900;
            font-family: 'Electrolize', monospace;
            margin-bottom: 1.5rem;
            text-shadow: 0 0 10px rgba(59, 130, 246, 0.5);
        }

        .card-product-name {
            font-size: 1.8rem;
            font-weight: 700;
            font-family: 'Electrolize', monospace;
            margin-bottom: 1.5rem;
            background: linear-gradient(135deg, #FFFFFF 0%, #E2E8F0 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .card-description {
            font-size: 1rem;
            color: #CBD5E1;
            font-family: 'Electrolize', monospace;
            font-weight: 400;
            line-height: 1.5;
        }

        /* Card-specific styling */
        .federal-card {
            border-color: #3B82F6;
        }

        .federal-card:hover {
            border-color: #60A5FA;
            box-shadow: 0 20px 50px rgba(59, 130, 246, 0.4);
        }

        .state-card {
            border-color: #1E40AF;
        }

        .state-card:hover {
            border-color: #3B82F6;
            box-shadow: 0 20px 50px rgba(30, 64, 175, 0.4);
        }

        .local-card {
            border-color: #1E3A8A;
        }

        .local-card:hover {
            border-color: #1E40AF;
            box-shadow: 0 20px 50px rgba(30, 58, 138, 0.4);
        }

        .suspect-identification-footer {
            font-size: 2rem;
            color: #ff6b6b;
            margin-top: 4rem;
            font-weight: 700;
            font-family: 'Electrolize', monospace;
            text-align: center;
        }

        /* Cover elements */
        .author-info {
            font-size: 1.3rem;
            color: var(--text-dim);
            margin: 2rem 0;
        }

        .author-info span {
            display: block;
            margin: 0.5rem 0;
        }

        .author-info-horizontal {
            font-size: 1.3rem;
            color: var(--text-dim);
            margin: 2rem 0;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 1rem;
            font-family: 'Electrolize', monospace;
            font-weight: 400;
        }

        .author-info-horizontal .separator {
            color: var(--accent);
            font-weight: bold;
            font-size: 1.1rem;
        }

        .surprise-button {
            background: var(--gradient-1);
            border: none;
            padding: 1.5rem 3rem;
            border-radius: 50px;
            color: white;
            font-size: 1.3rem;
            font-weight: 700;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: var(--shadow);
            margin-top: 2rem;
            position: relative;
            overflow: hidden;
            font-family: 'Electrolize', monospace;
            letter-spacing: 1px;
        }

        .surprise-button::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            transform: translate(-50%, -50%);
            transition: width 0.6s, height 0.6s;
        }

        .surprise-button:hover::before {
            width: 300px;
            height: 300px;
        }

        .surprise-button:hover {
            transform: scale(1.05);
            box-shadow: var(--blue-glow), var(--shadow-lg);
        }

        /* Content Slides */
        .slide-content {
            width: 100%;
            max-width: 1000px;
            margin: 0 auto;
        }

        .big-question {
            font-size: 3rem;
            font-weight: 700;
            margin-bottom: 2rem;
            color: var(--text);
            font-family: 'Electrolize', monospace;
        }

        .big-answer {
            font-size: 5rem;
            font-weight: 900;
            background: var(--gradient-1);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin: 2rem 0;
            font-family: 'Electrolize', monospace;
        }

        .statement {
            font-size: 2rem;
            margin: 2rem 0;
            line-height: 1.4;
            font-family: 'Electrolize', monospace;
        }

        .highlight {
            color: var(--primary);
            font-weight: 700;
        }

        .problem-text {
            color: var(--secondary);
            font-weight: 700;
        }

        .vision-text {
            color: var(--accent);
            font-weight: 700;
        }

        /* Countdown Animation */
        .countdown-container {
            font-size: 8rem;
            font-weight: 900;
            background: var(--gradient-1);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            animation: pulse 1s ease-in-out infinite;
            font-family: 'Electrolize', monospace;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); filter: brightness(1); }
            50% { transform: scale(1.1); filter: brightness(1.2); }
        }

        /* Puzzle Grid and Data Integration Styles */
        .puzzle-container {
            margin: 3rem 0;
        }

        .puzzle-title {
            font-size: 2.5rem;
            margin-bottom: 1rem;
            background: var(--gradient-1);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-weight: 900;
            font-family: 'Electrolize', monospace;
        }

        .puzzle-subtitle {
            font-size: 1.3rem;
            color: var(--text-dim);
            margin-bottom: 3rem;
            font-family: 'Electrolize', monospace;
        }

        /* Data Integration Puzzle Metaphor Styles */
        .data-puzzle-visual {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 2rem;
            margin: 3rem 0;
            flex-wrap: wrap;
        }

        .puzzle-piece-visual {
            width: 120px;
            height: 120px;
            background: var(--gradient-2);
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1rem;
            color: white;
            font-weight: bold;
            position: relative;
            transition: all 0.5s ease;
            animation: floatPiece 3s ease-in-out infinite;
            text-align: center;
            font-family: 'Electrolize', monospace;
        }

        .puzzle-piece-visual:nth-child(odd) {
            animation-delay: 0.5s;
        }

        .puzzle-piece-visual:hover {
            transform: scale(1.1) rotate(5deg);
            box-shadow: 0 0 30px rgba(59, 130, 246, 0.6);
        }

        @keyframes floatPiece {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }

        .puzzle-arrow {
            font-size: 3rem;
            color: var(--primary);
            animation: arrowPulse 2s ease-in-out infinite;
        }

        @keyframes arrowPulse {
            0%, 100% { opacity: 0.6; transform: scale(1); }
            50% { opacity: 1; transform: scale(1.2); }
        }

        .complete-picture {
            width: 200px;
            height: 200px;
            background: var(--gradient-1);
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
            color: white;
            font-weight: bold;
            text-align: center;
            box-shadow: 0 0 40px rgba(59, 130, 246, 0.8);
            animation: completePulse 2s ease-in-out infinite;
        }

        @keyframes completePulse {
            0%, 100% { box-shadow: 0 0 40px rgba(59, 130, 246, 0.8); }
            50% { box-shadow: 0 0 60px rgba(59, 130, 246, 1); }
        }

        .puzzle-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 2rem;
            max-width: 900px;
            margin: 0 auto;
        }

        .puzzle-piece {
            background: var(--dark-lighter);
            border: 3px solid var(--primary);
            border-radius: 20px;
            padding: 2rem;
            text-align: center;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .puzzle-piece::before {
            content: '';
            position: absolute;
            top: -100%;
            left: -100%;
            width: 300%;
            height: 300%;
            background: radial-gradient(circle, rgba(0, 212, 255, 0.1) 0%, transparent 70%);
            transition: all 0.5s ease;
        }

        .puzzle-piece:hover::before {
            top: -150%;
            left: -150%;
        }

        .puzzle-piece:hover {
            transform: scale(1.05);
            box-shadow: 0 0 30px rgba(0, 212, 255, 0.5);
            border-color: var(--accent);
        }

        .puzzle-piece h4 {
            color: var(--primary);
            margin-bottom: 1rem;
            font-size: 1.3rem;
        }

        .puzzle-piece .accuracy {
            color: var(--accent);
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0.5rem 0;
        }

        .puzzle-piece .price {
            color: var(--orange);
            font-weight: 600;
            font-size: 1rem;
            margin: 0.5rem 0;
        }

        .puzzle-piece .description {
            color: var(--text-dim);
            font-size: 0.9rem;
            margin-top: 1rem;
        }

        /* Architecture Flow */
        .architecture-container {
            margin: 3rem 0;
        }

        .flow-diagram {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 1.5rem;
            margin: 3rem 0;
            flex-wrap: wrap;
        }

        .flow-item {
            background: var(--dark-light);
            border: 2px solid var(--primary);
            border-radius: 15px;
            padding: 1.5rem;
            text-align: center;
            min-width: 150px;
            transition: all 0.3s ease;
        }

        .flow-item:hover {
            transform: scale(1.05);
            box-shadow: 0 0 20px rgba(0, 212, 255, 0.5);
        }

        .flow-item h4 {
            color: var(--primary);
            margin-bottom: 0.5rem;
        }

        .flow-item p {
            color: var(--text-dim);
            font-size: 0.9rem;
        }

        .flow-arrow {
            color: var(--primary);
            font-size: 2rem;
        }

        /* Enhancement Stats */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 2rem;
            margin: 3rem 0;
        }

        .stat-card {
            background: var(--dark-lighter);
            border-radius: 15px;
            padding: 2rem;
            text-align: center;
            border: 2px solid var(--dark-lighter);
            transition: all 0.3s ease;
        }

        .stat-card:hover {
            transform: scale(1.05);
            border-color: var(--primary);
        }

        .stat-number {
            font-size: 3rem;
            font-weight: 900;
            background: var(--gradient-1);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            display: block;
        }

        .stat-label {
            color: var(--text-dim);
            font-size: 1.1rem;
            margin-top: 0.5rem;
        }

        /* Feature Lists */
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 3rem;
            margin: 3rem 0;
            text-align: left;
        }

        .feature-card {
            background: var(--dark-light);
            border-radius: 20px;
            padding: 2rem;
            border: 1px solid var(--dark-lighter);
        }

        .feature-card h3 {
            color: var(--primary);
            margin-bottom: 1.5rem;
            font-size: 1.6rem;
        }

        .feature-list {
            list-style: none;
            padding: 0;
        }

        .feature-list li {
            padding: 0.8rem 0;
            border-bottom: 1px solid var(--dark-lighter);
            display: flex;
            align-items: center;
            gap: 1rem;
            color: var(--text-dim);
        }

        .feature-list li::before {
            content: '✓';
            color: var(--accent);
            font-size: 1.3rem;
            font-weight: bold;
        }

        /* Government Cards */
        .gov-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 2rem;
            margin: 3rem 0;
        }

        .gov-card {
            background: var(--dark-light);
            border-radius: 20px;
            padding: 2rem;
            border: 2px solid var(--dark-lighter);
            transition: all 0.3s ease;
        }

        .gov-card:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-lg);
        }

        .gov-card.fed {
            border-color: var(--secondary);
        }

        .gov-card.state {
            border-color: var(--accent);
        }

        .gov-card.county {
            border-color: var(--purple);
        }

        .gov-card h3 {
            font-size: 1.8rem;
            margin-bottom: 1rem;
        }

        .gov-card.fed h3 {
            color: var(--secondary);
        }

        .gov-card.state h3 {
            color: var(--accent);
        }

        .gov-card.county h3 {
            color: var(--purple);
        }

        /* Compliance Grid */
        .compliance-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 2rem;
            margin: 3rem 0;
        }

        .compliance-card {
            background: var(--dark-light);
            border-radius: 20px;
            padding: 2rem;
            border: 2px solid var(--dark-lighter);
        }

        .compliance-card h3 {
            color: var(--primary);
            margin-bottom: 1.5rem;
            font-size: 1.6rem;
        }

        /* Google Cloud Section */
        .cloud-services {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 2rem;
            margin: 3rem 0;
        }

        .cloud-service {
            background: var(--dark-lighter);
            border-radius: 15px;
            padding: 1.5rem;
            text-align: center;
            border: 2px solid transparent;
            transition: all 0.3s ease;
        }

        .cloud-service:hover {
            border-color: var(--primary);
            transform: translateY(-5px);
        }

        .cloud-service h4 {
            color: var(--primary);
            margin-bottom: 1rem;
        }

        .cloud-service p {
            color: var(--text-dim);
            font-size: 0.9rem;
        }

        /* Image Enhancement Technologies */
        .enhancement-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 3rem;
            margin: 3rem 0;
        }

        .enhancement-card {
            background: var(--dark-light);
            border-radius: 20px;
            padding: 2rem;
            border: 2px solid var(--dark-lighter);
            position: relative;
            overflow: hidden;
        }

        .enhancement-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, rgba(0, 212, 255, 0.1) 0%, transparent 100%);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .enhancement-card:hover::before {
            opacity: 1;
        }

        .enhancement-card h3 {
            color: var(--accent);
            margin-bottom: 1.5rem;
            font-size: 1.6rem;
        }

        /* Call to Action */
        .cta-container {
            background: var(--gradient-1);
            border-radius: 30px;
            padding: 3rem;
            margin: 2rem 0;
            text-align: center;
            color: white;
        }

        .cta-container h3 {
            color: white;
            font-size: 2.5rem;
            margin-bottom: 2rem;
        }

        .cta-container p {
            font-size: 1.3rem;
            margin: 2rem 0;
            line-height: 1.6;
        }

        .cta-button {
            background: white;
            color: var(--dark);
            border: none;
            padding: 1.5rem 3rem;
            border-radius: 50px;
            font-size: 1.2rem;
            font-weight: 700;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-top: 2rem;
        }

        .cta-button:hover {
            transform: scale(1.05);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        }

        /* Controls hint */
        .controls-hint {
            position: fixed;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(26, 26, 26, 0.95);
            backdrop-filter: blur(10px);
            padding: 10px 20px;
            border-radius: 25px;
            color: var(--text-dim);
            font-size: 0.9rem;
            z-index: 1000;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .puzzle-grid {
                grid-template-columns: 1fr;
                gap: 1rem;
            }

            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 1rem;
            }

            .feature-grid {
                grid-template-columns: 1fr;
                gap: 1.5rem;
            }

            .gov-grid {
                grid-template-columns: 1fr;
                gap: 1rem;
            }

            .cloud-services {
                grid-template-columns: 1fr;
                gap: 1rem;
            }

            .enhancement-grid {
                grid-template-columns: 1fr;
                gap: 1.5rem;
            }

            .compliance-grid {
                grid-template-columns: 1fr;
                gap: 1.5rem;
            }

            .flow-diagram {
                flex-direction: column;
            }

            .flow-arrow {
                transform: rotate(90deg);
            }

            .sub-logos {
                flex-direction: column;
                gap: 1.5rem;
            }

            .facetrace-logo {
                font-size: 3rem;
            }

            .big-question {
                font-size: 2rem;
            }

            .big-answer {
                font-size: 3rem;
            }

            .statement {
                font-size: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <!-- Animated Background -->
    <div class="bg-animation"></div>

    <!-- Unified Progress Bar and Navigation -->
    <div class="progress-container">
        <div class="progress-info">
            <span class="page-number">Slide <span id="currentSlide">1</span> of <span id="totalSlides">28</span></span>
            <span style="color: var(--text-dim); font-size: 0.85rem;">FaceTrace</span>
        </div>
        <div class="progress-bar-bg">
            <div class="progress-bar" id="progressBar"></div>
        </div>
        <div class="nav-bar" id="navBar">
            <span class="nav-item active" data-section="intro">Intro</span>
            <span class="nav-item" data-section="executive">Executive</span>
            <span class="nav-item" data-section="behind-face">Behind Face</span>
            <span class="nav-item" data-section="data-puzzle">Data Puzzle</span>
            <span class="nav-item" data-section="data-types">Data Types</span>
            <span class="nav-item" data-section="architecture">Architecture</span>
            <span class="nav-item" data-section="enhancement">Enhancement</span>
            <span class="nav-item" data-section="cloud">Cloud</span>
            <span class="nav-item" data-section="compliance">Compliance</span>
            <span class="nav-item" data-section="conclusion">Conclusion</span>
        </div>
    </div>

    <!-- Integrated Navigation Bar (now part of progress container) -->

    <!-- Presentation Container -->
    <div class="presentation-container">
        
        <!-- Slide 1: Enhanced Cover Page -->
        <div class="slide active" data-slide="1" data-section="intro">
            <div class="slide-content-wrapper">
                <div class="cover-page-container-enhanced">
                    <!-- Main Logo Section -->
                    <div class="main-logo-section fade-element visible">
                        <div class="logo-container-with-svg-centered">
                            <!-- Centered SVG Logo -->
                            <div class="svg-logo-wrapper-centered">
                                <svg class="facetrace-svg-logo-centered" viewBox="0 0 100 100" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <!-- Face outline - perfectly centered -->
                                    <polygon
                                        points="50,10 25,25 15,50 25,75 50,90 75,75 85,50 75,25"
                                        stroke="white"
                                        stroke-width="4"
                                        stroke-opacity="1"
                                        fill="none"
                                        stroke-linejoin="round"
                                    />

                                    <!-- Biometric identification grid -->
                                    <path d="M50,10 L50,90" stroke="white" stroke-width="0.9" stroke-opacity="0.5" stroke-dasharray="3 2" />
                                    <path d="M15,50 L85,50" stroke="white" stroke-width="0.9" stroke-opacity="0.5" stroke-dasharray="3 2" />
                                    <path d="M25,25 L75,75" stroke="white" stroke-width="0.9" stroke-opacity="0.5" stroke-dasharray="3 2" />
                                    <path d="M25,75 L75,25" stroke="white" stroke-width="0.9" stroke-opacity="0.5" stroke-dasharray="3 2" />

                                    <!-- Eyes - geometric -->
                                    <rect x="32" y="40" width="8" height="7" rx="1" stroke="white" stroke-width="2.5" stroke-opacity="1" fill="none" />
                                    <rect x="60" y="40" width="8" height="7" rx="1" stroke="white" stroke-width="2.5" stroke-opacity="1" fill="none" />

                                    <!-- Mouth - straight line for serious expression -->
                                    <path d="M35,65 L65,65" stroke="white" stroke-width="2" stroke-opacity="0.9" fill="none" stroke-linecap="round" />

                                    <!-- Intersection points -->
                                    <circle cx="50" cy="10" r="1.5" fill="white" fill-opacity="1" />
                                    <circle cx="25" cy="25" r="1.5" fill="white" fill-opacity="1" />
                                    <circle cx="15" cy="50" r="1.5" fill="white" fill-opacity="1" />
                                    <circle cx="25" cy="75" r="1.5" fill="white" fill-opacity="1" />
                                    <circle cx="50" cy="90" r="1.5" fill="white" fill-opacity="1" />
                                    <circle cx="75" cy="75" r="1.5" fill="white" fill-opacity="1" />
                                    <circle cx="85" cy="50" r="1.5" fill="white" fill-opacity="1" />
                                    <circle cx="75" cy="25" r="1.5" fill="white" fill-opacity="1" />

                                    <!-- Biometric measurement points -->
                                    <circle cx="50" cy="45" r="1" fill="white" fill-opacity="0.7" />
                                    <circle cx="50" cy="65" r="1" fill="white" fill-opacity="0.7" />
                                    <circle cx="35" cy="50" r="1" fill="white" fill-opacity="0.7" />
                                    <circle cx="65" cy="50" r="1" fill="white" fill-opacity="0.7" />

                                    <!-- Scanner effect -->
                                    <path
                                        d="M15 50 L85 50"
                                        stroke="#00FFFF"
                                        stroke-width="1.2"
                                        stroke-opacity="0.7"
                                        class="scanner-line"
                                    />
                                </svg>
                            </div>

                            <!-- Text Logo -->
                            <div class="text-logo-container-enhanced">
                                <div class="facetrace-logo-enhanced">
                                    <span class="facetrace-text">FaceTrace</span>
                                    <div class="animated-dot"></div>
                                    <span class="pro-text">Gov</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Enhanced Subtitle with Typewriter Effect -->
                    <div class="subtitle-section fade-element visible">
                        <div class="logo-subtitle-enhanced">
                            <span id="typewriter-text"></span>
                            <span class="typewriter-cursor" id="typewriter-cursor">|</span>
                        </div>
                    </div>

                    <!-- Vision Statement -->
                    <div class="vision-section fade-element visible">
                        <div class="vision-statement">Make America Safe Again</div>
                    </div>

                    <!-- Author Info Section -->
                    <div class="author-section fade-element visible">
                        <div class="author-info-horizontal">
                            <span>Bryce Bayens</span>
                            <span class="separator">•</span>
                            <span>June 2025</span>
                            <span class="separator">•</span>
                            <span>FaceTrace.pro</span>
                        </div>
                    </div>



                </div>
            </div>
        </div>



        <!-- Slide 2: What is the most valuable asset? -->
        <div class="slide" data-slide="2" data-section="executive">
            <div class="slide-content-wrapper">
                <div class="slide-content">
                    <div class="fade-element">
                        <div class="big-question">What is the most valuable asset in the world?</div>
                    </div>
                </div>
            </div>
        </div>



        <!-- Slide 3: DATA -->
        <div class="slide" data-slide="3" data-section="executive">
            <div class="slide-content-wrapper">
                <div class="slide-content">
                    <div class="fade-element">
                        <div class="big-answer">DATA</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Slide 4: The Problem -->
        <div class="slide" data-slide="4" data-section="executive">
            <div class="slide-content-wrapper">
                <div class="slide-content">
                    <div class="fade-element">
                        <div class="statement">
                            <span class="problem-text">The Problem:</span><br>
                            Agencies crowdsource information to identify suspects.
                        </div>
                    </div>
                    <div class="fade-element">
                        <div style="font-size: 2rem; color: var(--secondary); margin-top: 3rem; font-family: 'Electrolize', monospace;">
                            Time is critical
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Slide 5: Architecture Flow (The Solution) -->
        <div class="slide" data-slide="5" data-section="architecture">
            <div class="slide-content-wrapper">
                <div class="slide-content">
                    <div class="fade-element">
                        <div class="puzzle-title">Advanced Architecture</div>
                        <div class="puzzle-subtitle">From grainy footage to positive identification</div>

                        <div class="flow-diagram">
                            <div class="flow-item fade-element">
                                <h4>Input</h4>
                                <p>Security cameras</p>
                            </div>
                            <span class="flow-arrow fade-element">→</span>
                            <div class="flow-item fade-element">
                                <h4>Enhancement</h4>
                                <p>AI upscaling</p>
                            </div>
                            <span class="flow-arrow fade-element">→</span>
                            <div class="flow-item fade-element">
                                <h4>Detection</h4>
                                <p>Face matching</p>
                            </div>
                            <span class="flow-arrow fade-element">→</span>
                            <div class="flow-item fade-element">
                                <h4>Data Fusion</h4>
                                <p>Skip tracing</p>
                            </div>
                            <span class="flow-arrow fade-element">→</span>
                            <div class="flow-item fade-element">
                                <h4>Results</h4>
                                <p>Full profile</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Slide 9: Who's Behind The Face? -->
        <div class="slide" data-slide="9" data-section="behind-face">
            <div class="slide-content-wrapper">
                <div class="slide-content">
                    <div class="fade-element">
                        <div class="puzzle-title">Who's Behind The Face?</div>
                        <div class="puzzle-subtitle">Human-centered facial recognition technology</div>
                    </div>

                    <div class="fade-element">
                        <div style="display: flex; justify-content: center; align-items: center; gap: 3rem; margin: 3rem 0; flex-wrap: wrap;">
                            <!-- Human representation -->
                            <div style="width: 200px; height: 200px; background: var(--gradient-1); border-radius: 50%; display: flex; align-items: center; justify-content: center; position: relative; animation: humanPulse 3s ease-in-out infinite;">
                                <div style="font-size: 4rem; color: white;">HUMAN</div>
                                <div style="position: absolute; top: -10px; right: -10px; width: 40px; height: 40px; background: var(--secondary); border-radius: 50%; display: flex; align-items: center; justify-content: center; font-size: 1rem; animation: techPulse 2s ease-in-out infinite;">ID</div>
                            </div>

                            <div style="font-size: 3rem; color: var(--primary); animation: arrowPulse 2s ease-in-out infinite;">→</div>

                            <!-- Technology representation -->
                            <div style="width: 200px; height: 200px; background: var(--gradient-2); border-radius: 20px; display: flex; align-items: center; justify-content: center; position: relative; animation: techFloat 4s ease-in-out infinite;">
                                <div style="font-size: 2rem; color: white;">AI TECH</div>
                                <div style="position: absolute; bottom: -10px; left: -10px; width: 40px; height: 40px; background: var(--accent); border-radius: 50%; display: flex; align-items: center; justify-content: center; font-size: 1rem; animation: dataPulse 1.5s ease-in-out infinite;">DATA</div>
                            </div>
                        </div>
                    </div>

                    <div class="fade-element">
                        <div style="font-size: 1.8rem; color: var(--text-bright); text-align: center; font-family: 'Electrolize', monospace; margin: 2rem 0;">
                            "Every algorithm serves human innovation,<br>
                            every search serves human purpose,<br>
                            every match serves human safety."
                        </div>
                    </div>

                    <div class="fade-element">
                        <div style="font-size: 2.5rem; color: var(--secondary); font-weight: 900; font-family: 'Electrolize', monospace; text-align: center;">
                            Technology Serving Humanity
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <style>
            @keyframes humanPulse {
                0%, 100% { box-shadow: 0 0 20px rgba(59, 130, 246, 0.5); }
                50% { box-shadow: 0 0 40px rgba(59, 130, 246, 0.8); }
            }

            @keyframes techFloat {
                0%, 100% { transform: translateY(0px); }
                50% { transform: translateY(-15px); }
            }

            @keyframes techPulse {
                0%, 100% { transform: scale(1); }
                50% { transform: scale(1.2); }
            }

            @keyframes dataPulse {
                0%, 100% { transform: scale(1) rotate(0deg); }
                50% { transform: scale(1.3) rotate(180deg); }
            }
        </style>

        <!-- Slide 10: Data as Puzzle Pieces -->
        <div class="slide" data-slide="10" data-section="data-puzzle">
            <div class="slide-content-wrapper">
                <div class="slide-content">
                    <div class="fade-element">
                        <div class="puzzle-title">Data as Puzzle Pieces</div>
                        <div class="puzzle-subtitle">Individual data points combine to reveal complete identity profiles</div>
                    </div>

                    <div class="fade-element">
                        <div class="data-puzzle-visual">
                            <div class="puzzle-piece-visual">PHONE</div>
                            <div class="puzzle-piece-visual">ADDRESS</div>
                            <div class="puzzle-piece-visual">VEHICLE</div>
                            <div class="puzzle-arrow">+</div>
                            <div class="puzzle-piece-visual">SOCIAL</div>
                            <div class="puzzle-piece-visual">WORK</div>
                            <div class="puzzle-piece-visual">EMAIL</div>
                        </div>
                    </div>

                    <div class="fade-element">
                        <div style="display: flex; justify-content: center; margin: 3rem 0;">
                            <div class="puzzle-arrow">↓</div>
                        </div>
                    </div>

                    <div class="fade-element">
                        <div style="display: flex; justify-content: center;">
                            <div class="complete-picture">
                                Complete<br>Identity<br>Profile
                            </div>
                        </div>
                    </div>

                    <div class="fade-element">
                        <div style="font-size: 1.5rem; color: var(--accent); text-align: center; font-family: 'Electrolize', monospace; margin-top: 2rem;">
                            "Individual pieces mean nothing.<br>Together, they solve the puzzle."
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Slide 11: Data Types & Dataset Schemas Overview -->
        <div class="slide" data-slide="11" data-section="data-types">
            <div class="slide-content-wrapper">
                <div class="slide-content">
                    <div class="fade-element">
                        <div class="puzzle-title">Data Types & Dataset Schemas Overview</div>
                        <div class="puzzle-subtitle">IDI Core TLO People API Capabilities</div>
                    </div>

                    <div class="fade-element">
                        <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 2rem; margin: 3rem 0;">
                            <div style="background: var(--dark-light); border: 2px solid var(--primary); border-radius: 15px; padding: 2rem; text-align: center;">
                                <div style="font-size: 1.5rem; margin-bottom: 1rem; color: var(--primary); font-weight: bold;">PERSONAL</div>
                                <h3 style="color: var(--primary); margin-bottom: 1rem;">Personal Data</h3>
                                <p style="color: var(--text-dim);">Core identity attributes and demographics</p>
                            </div>

                            <div style="background: var(--dark-light); border: 2px solid var(--accent); border-radius: 15px; padding: 2rem; text-align: center;">
                                <div style="font-size: 1.5rem; margin-bottom: 1rem; color: var(--accent); font-weight: bold;">CONTACT</div>
                                <h3 style="color: var(--accent); margin-bottom: 1rem;">Contact Info</h3>
                                <p style="color: var(--text-dim);">Addresses, phones, emails with history</p>
                            </div>

                            <div style="background: var(--dark-light); border: 2px solid var(--secondary); border-radius: 15px; padding: 2rem; text-align: center;">
                                <div style="font-size: 1.5rem; margin-bottom: 1rem; color: var(--secondary); font-weight: bold;">DIGITAL</div>
                                <h3 style="color: var(--secondary); margin-bottom: 1rem;">Digital Footprint</h3>
                                <p style="color: var(--text-dim);">Social media and online presence</p>
                            </div>

                            <div style="background: var(--dark-light); border: 2px solid var(--orange); border-radius: 15px; padding: 2rem; text-align: center;">
                                <div style="font-size: 1.5rem; margin-bottom: 1rem; color: var(--orange); font-weight: bold;">LEGAL</div>
                                <h3 style="color: var(--orange); margin-bottom: 1rem;">Legal Records</h3>
                                <p style="color: var(--text-dim);">Criminal and court records</p>
                            </div>

                            <div style="background: var(--dark-light); border: 2px solid var(--green); border-radius: 15px; padding: 2rem; text-align: center;">
                                <div style="font-size: 1.5rem; margin-bottom: 1rem; color: var(--green); font-weight: bold;">ASSETS</div>
                                <h3 style="color: var(--green); margin-bottom: 1rem;">Assets</h3>
                                <p style="color: var(--text-dim);">Property and vehicle ownership</p>
                            </div>

                            <div style="background: var(--dark-light); border: 2px solid var(--purple); border-radius: 15px; padding: 2rem; text-align: center;">
                                <div style="font-size: 1.5rem; margin-bottom: 1rem; color: var(--purple); font-weight: bold;">NETWORK</div>
                                <h3 style="color: var(--purple); margin-bottom: 1rem;">Relationships</h3>
                                <p style="color: var(--text-dim);">Family and associate networks</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Slide 12: Personal Information Data -->
        <div class="slide" data-slide="12" data-section="data-types">
            <div class="slide-content-wrapper">
                <div class="slide-content">
                    <div class="fade-element">
                        <div class="puzzle-title">Personal Information Data</div>
                        <div class="puzzle-subtitle">Core identity attributes with comprehensive demographic coverage</div>
                    </div>

                    <div class="fade-element">
                        <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 3rem; margin: 3rem 0;">
                            <div style="background: var(--dark-light); border-radius: 20px; padding: 2rem;">
                                <h3 style="color: var(--primary); margin-bottom: 2rem; font-size: 1.8rem;">Identity Core</h3>
                                <ul style="list-style: none; padding: 0;">
                                    <li style="padding: 0.8rem 0; border-bottom: 1px solid var(--dark-lighter); display: flex; align-items: center; gap: 1rem;">
                                        <span style="color: var(--primary); font-weight: bold;">•</span>
                                        <span style="color: var(--text-dim);">Full Legal Name</span>
                                    </li>
                                    <li style="padding: 0.8rem 0; border-bottom: 1px solid var(--dark-lighter); display: flex; align-items: center; gap: 1rem;">
                                        <span style="color: var(--primary); font-weight: bold;">•</span>
                                        <span style="color: var(--text-dim);">Date of Birth</span>
                                    </li>
                                    <li style="padding: 0.8rem 0; border-bottom: 1px solid var(--dark-lighter); display: flex; align-items: center; gap: 1rem;">
                                        <span style="color: var(--primary); font-weight: bold;">•</span>
                                        <span style="color: var(--text-dim);">Social Security Number</span>
                                    </li>
                                    <li style="padding: 0.8rem 0; border-bottom: 1px solid var(--dark-lighter); display: flex; align-items: center; gap: 1rem;">
                                        <span style="color: var(--primary); font-weight: bold;">•</span>
                                        <span style="color: var(--text-dim);">Gender</span>
                                    </li>
                                </ul>
                            </div>

                            <div style="background: var(--dark-light); border-radius: 20px; padding: 2rem;">
                                <h3 style="color: var(--accent); margin-bottom: 2rem; font-size: 1.8rem;">Demographics</h3>
                                <ul style="list-style: none; padding: 0;">
                                    <li style="padding: 0.8rem 0; border-bottom: 1px solid var(--dark-lighter); display: flex; align-items: center; gap: 1rem;">
                                        <span style="color: var(--accent); font-weight: bold;">•</span>
                                        <span style="color: var(--text-dim);">Marital Status</span>
                                    </li>
                                    <li style="padding: 0.8rem 0; border-bottom: 1px solid var(--dark-lighter); display: flex; align-items: center; gap: 1rem;">
                                        <span style="color: var(--accent); font-weight: bold;">•</span>
                                        <span style="color: var(--text-dim);">Age Range</span>
                                    </li>
                                    <li style="padding: 0.8rem 0; border-bottom: 1px solid var(--dark-lighter); display: flex; align-items: center; gap: 1rem;">
                                        <span style="color: var(--accent); font-weight: bold;">•</span>
                                        <span style="color: var(--text-dim);">Household Composition</span>
                                    </li>
                                    <li style="padding: 0.8rem 0; border-bottom: 1px solid var(--dark-lighter); display: flex; align-items: center; gap: 1rem;">
                                        <span style="color: var(--accent); font-weight: bold;">•</span>
                                        <span style="color: var(--text-dim);">Income Estimates</span>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Slide 13: Contact Information Data -->
        <div class="slide" data-slide="13" data-section="data-types">
            <div class="slide-content-wrapper">
                <div class="slide-content">
                    <div class="fade-element">
                        <div class="puzzle-title">Contact Information Data</div>
                        <div class="puzzle-subtitle">Address history, phone numbers, and email verification with timeline representation</div>
                    </div>

                    <div class="fade-element">
                        <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 2rem; margin: 3rem 0;">
                            <div style="background: var(--dark-light); border: 2px solid var(--primary); border-radius: 20px; padding: 2rem;">
                                <div style="text-align: center; margin-bottom: 2rem;">
                                    <div style="font-size: 1.5rem; margin-bottom: 1rem; color: var(--primary); font-weight: bold;">ADDRESS</div>
                                    <h3 style="color: var(--primary); font-size: 1.6rem;">Address History</h3>
                                </div>
                                <ul style="list-style: none; padding: 0;">
                                    <li style="padding: 0.5rem 0; color: var(--text-dim);">• Up to 20 years of history</li>
                                    <li style="padding: 0.5rem 0; color: var(--text-dim);">• Current and previous addresses</li>
                                    <li style="padding: 0.5rem 0; color: var(--text-dim);">• Property ownership records</li>
                                    <li style="padding: 0.5rem 0; color: var(--text-dim);">• Neighborhood demographics</li>
                                </ul>
                            </div>

                            <div style="background: var(--dark-light); border: 2px solid var(--accent); border-radius: 20px; padding: 2rem;">
                                <div style="text-align: center; margin-bottom: 2rem;">
                                    <div style="font-size: 1.5rem; margin-bottom: 1rem; color: var(--accent); font-weight: bold;">PHONE</div>
                                    <h3 style="color: var(--accent); font-size: 1.6rem;">Phone Numbers</h3>
                                </div>
                                <ul style="list-style: none; padding: 0;">
                                    <li style="padding: 0.5rem 0; color: var(--text-dim);">• Mobile and landline</li>
                                    <li style="padding: 0.5rem 0; color: var(--text-dim);">• Carrier information</li>
                                    <li style="padding: 0.5rem 0; color: var(--text-dim);">• Verification scores</li>
                                    <li style="padding: 0.5rem 0; color: var(--text-dim);">• Usage patterns</li>
                                </ul>
                            </div>

                            <div style="background: var(--dark-light); border: 2px solid var(--secondary); border-radius: 20px; padding: 2rem;">
                                <div style="text-align: center; margin-bottom: 2rem;">
                                    <div style="font-size: 1.5rem; margin-bottom: 1rem; color: var(--secondary); font-weight: bold;">EMAIL</div>
                                    <h3 style="color: var(--secondary); font-size: 1.6rem;">Email Addresses</h3>
                                </div>
                                <ul style="list-style: none; padding: 0;">
                                    <li style="padding: 0.5rem 0; color: var(--text-dim);">• Personal and business emails</li>
                                    <li style="padding: 0.5rem 0; color: var(--text-dim);">• Domain analysis</li>
                                    <li style="padding: 0.5rem 0; color: var(--text-dim);">• Activity verification</li>
                                    <li style="padding: 0.5rem 0; color: var(--text-dim);">• Breach monitoring</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <div class="fade-element">
                        <div style="background: var(--gradient-1); border-radius: 20px; padding: 2rem; margin: 2rem 0; text-align: center;">
                            <h3 style="color: white; margin-bottom: 1rem;">Timeline Visualization</h3>
                            <div style="display: flex; justify-content: space-between; align-items: center; color: white;">
                                <div>2004</div>
                                <div style="flex: 1; height: 4px; background: rgba(255,255,255,0.3); margin: 0 1rem; position: relative;">
                                    <div style="position: absolute; left: 25%; width: 10px; height: 10px; background: white; border-radius: 50%; top: -3px;"></div>
                                    <div style="position: absolute; left: 60%; width: 10px; height: 10px; background: white; border-radius: 50%; top: -3px;"></div>
                                    <div style="position: absolute; left: 90%; width: 10px; height: 10px; background: white; border-radius: 50%; top: -3px;"></div>
                                </div>
                                <div>2024</div>
                            </div>
                            <p style="color: rgba(255,255,255,0.8); margin-top: 1rem;">20-year comprehensive contact history tracking</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Slide 14: Digital Footprint & Social Media -->
        <div class="slide" data-slide="14" data-section="data-types">
            <div class="slide-content-wrapper">
                <div class="slide-content">
                    <div class="fade-element">
                        <div class="puzzle-title">Digital Footprint & Social Media</div>
                        <div class="puzzle-subtitle">Social platforms, usernames, and online presence mapping</div>
                    </div>

                    <div class="fade-element">
                        <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 3rem; margin: 3rem 0;">
                            <div style="background: var(--dark-light); border-radius: 20px; padding: 2rem;">
                                <h3 style="color: var(--primary); margin-bottom: 2rem; font-size: 1.8rem;">Social Media Platforms</h3>
                                <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 1rem;">
                                    <div style="background: var(--dark-lighter); padding: 1rem; border-radius: 10px; text-align: center;">
                                        <div style="font-size: 1rem; margin-bottom: 0.5rem; color: var(--primary); font-weight: bold;">FB</div>
                                        <div style="color: var(--text-dim);">Facebook</div>
                                    </div>
                                    <div style="background: var(--dark-lighter); padding: 1rem; border-radius: 10px; text-align: center;">
                                        <div style="font-size: 1rem; margin-bottom: 0.5rem; color: var(--primary); font-weight: bold;">TW</div>
                                        <div style="color: var(--text-dim);">Twitter</div>
                                    </div>
                                    <div style="background: var(--dark-lighter); padding: 1rem; border-radius: 10px; text-align: center;">
                                        <div style="font-size: 1rem; margin-bottom: 0.5rem; color: var(--primary); font-weight: bold;">LI</div>
                                        <div style="color: var(--text-dim);">LinkedIn</div>
                                    </div>
                                    <div style="background: var(--dark-lighter); padding: 1rem; border-radius: 10px; text-align: center;">
                                        <div style="font-size: 1rem; margin-bottom: 0.5rem; color: var(--primary); font-weight: bold;">IG</div>
                                        <div style="color: var(--text-dim);">Instagram</div>
                                    </div>
                                    <div style="background: var(--dark-lighter); padding: 1rem; border-radius: 10px; text-align: center;">
                                        <div style="font-size: 1rem; margin-bottom: 0.5rem; color: var(--primary); font-weight: bold;">TT</div>
                                        <div style="color: var(--text-dim);">TikTok</div>
                                    </div>
                                    <div style="background: var(--dark-lighter); padding: 1rem; border-radius: 10px; text-align: center;">
                                        <div style="font-size: 1rem; margin-bottom: 0.5rem; color: var(--primary); font-weight: bold;">DA</div>
                                        <div style="color: var(--text-dim);">Dating Apps</div>
                                    </div>
                                </div>
                            </div>

                            <div style="background: var(--dark-light); border-radius: 20px; padding: 2rem;">
                                <h3 style="color: var(--accent); margin-bottom: 2rem; font-size: 1.8rem;">Online Presence</h3>
                                <ul style="list-style: none; padding: 0;">
                                    <li style="padding: 0.8rem 0; border-bottom: 1px solid var(--dark-lighter); display: flex; align-items: center; gap: 1rem;">
                                        <span style="color: var(--accent); font-weight: bold;">•</span>
                                        <span style="color: var(--text-dim);">Usernames & Handles</span>
                                    </li>
                                    <li style="padding: 0.8rem 0; border-bottom: 1px solid var(--dark-lighter); display: flex; align-items: center; gap: 1rem;">
                                        <span style="color: var(--accent); font-weight: bold;">•</span>
                                        <span style="color: var(--text-dim);">Website Associations</span>
                                    </li>
                                    <li style="padding: 0.8rem 0; border-bottom: 1px solid var(--dark-lighter); display: flex; align-items: center; gap: 1rem;">
                                        <span style="color: var(--accent); font-weight: bold;">•</span>
                                        <span style="color: var(--text-dim);">Gaming Profiles</span>
                                    </li>
                                    <li style="padding: 0.8rem 0; border-bottom: 1px solid var(--dark-lighter); display: flex; align-items: center; gap: 1rem;">
                                        <span style="color: var(--accent); font-weight: bold;">•</span>
                                        <span style="color: var(--text-dim);">Forum Participation</span>
                                    </li>
                                    <li style="padding: 0.8rem 0; border-bottom: 1px solid var(--dark-lighter); display: flex; align-items: center; gap: 1rem;">
                                        <span style="color: var(--accent); font-weight: bold;">•</span>
                                        <span style="color: var(--text-dim);">Blog & Content</span>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <div class="fade-element">
                        <div style="text-align: center; margin: 2rem 0;">
                            <div style="font-size: 1.5rem; color: var(--secondary); font-weight: 600; margin-bottom: 1rem;">Social Network Mapping</div>
                            <div style="display: flex; justify-content: center; align-items: center; gap: 2rem;">
                                <div style="width: 60px; height: 60px; background: var(--primary); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-size: 0.9rem;">USER</div>
                                <div style="width: 2px; height: 30px; background: var(--accent);"></div>
                                <div style="display: flex; flex-direction: column; gap: 1rem;">
                                    <div style="width: 40px; height: 40px; background: var(--accent); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-size: 0.7rem;">SOC</div>
                                    <div style="width: 40px; height: 40px; background: var(--secondary); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-size: 0.7rem;">BIZ</div>
                                    <div style="width: 40px; height: 40px; background: var(--green); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-size: 0.7rem;">LOC</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Slide 15: Criminal & Court Records -->
        <div class="slide" data-slide="15" data-section="data-types">
            <div class="slide-content-wrapper">
                <div class="slide-content">
                    <div class="fade-element">
                        <div class="puzzle-title">Criminal & Court Records</div>
                        <div class="puzzle-subtitle">Professional law enforcement styling with comprehensive legal history</div>
                    </div>

                    <div class="fade-element">
                        <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 3rem; margin: 3rem 0;">
                            <div style="background: linear-gradient(145deg, #1a1a1a, #2a2a2a); border: 2px solid var(--secondary); border-radius: 20px; padding: 2rem;">
                                <div style="text-align: center; margin-bottom: 2rem;">
                                    <div style="font-size: 3rem; margin-bottom: 1rem;">🚔</div>
                                    <h3 style="color: var(--secondary); font-size: 1.8rem;">Criminal Records</h3>
                                </div>
                                <ul style="list-style: none; padding: 0;">
                                    <li style="padding: 0.8rem 0; border-bottom: 1px solid var(--dark-lighter); display: flex; align-items: center; gap: 1rem;">
                                        <span style="color: var(--secondary);">•</span>
                                        <span style="color: var(--text-dim);">Criminal convictions</span>
                                    </li>
                                    <li style="padding: 0.8rem 0; border-bottom: 1px solid var(--dark-lighter); display: flex; align-items: center; gap: 1rem;">
                                        <span style="color: var(--secondary);">•</span>
                                        <span style="color: var(--text-dim);">Arrest records</span>
                                    </li>
                                    <li style="padding: 0.8rem 0; border-bottom: 1px solid var(--dark-lighter); display: flex; align-items: center; gap: 1rem;">
                                        <span style="color: var(--secondary);">•</span>
                                        <span style="color: var(--text-dim);">Sex offender status</span>
                                    </li>
                                    <li style="padding: 0.8rem 0; border-bottom: 1px solid var(--dark-lighter); display: flex; align-items: center; gap: 1rem;">
                                        <span style="color: var(--secondary);">•</span>
                                        <span style="color: var(--text-dim);">Warrant information</span>
                                    </li>
                                </ul>
                            </div>

                            <div style="background: linear-gradient(145deg, #1a1a1a, #2a2a2a); border: 2px solid var(--orange); border-radius: 20px; padding: 2rem;">
                                <div style="text-align: center; margin-bottom: 2rem;">
                                    <div style="font-size: 3rem; margin-bottom: 1rem;">⚖️</div>
                                    <h3 style="color: var(--orange); font-size: 1.8rem;">Court Records</h3>
                                </div>
                                <ul style="list-style: none; padding: 0;">
                                    <li style="padding: 0.8rem 0; border-bottom: 1px solid var(--dark-lighter); display: flex; align-items: center; gap: 1rem;">
                                        <span style="color: var(--orange);">•</span>
                                        <span style="color: var(--text-dim);">Civil cases</span>
                                    </li>
                                    <li style="padding: 0.8rem 0; border-bottom: 1px solid var(--dark-lighter); display: flex; align-items: center; gap: 1rem;">
                                        <span style="color: var(--orange);">•</span>
                                        <span style="color: var(--text-dim);">Bankruptcy filings</span>
                                    </li>
                                    <li style="padding: 0.8rem 0; border-bottom: 1px solid var(--dark-lighter); display: flex; align-items: center; gap: 1rem;">
                                        <span style="color: var(--orange);">•</span>
                                        <span style="color: var(--text-dim);">Liens and judgments</span>
                                    </li>
                                    <li style="padding: 0.8rem 0; border-bottom: 1px solid var(--dark-lighter); display: flex; align-items: center; gap: 1rem;">
                                        <span style="color: var(--orange);">•</span>
                                        <span style="color: var(--text-dim);">Traffic violations</span>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <div class="fade-element">
                        <div style="background: linear-gradient(135deg, var(--secondary), var(--orange)); border-radius: 20px; padding: 2rem; margin: 2rem 0; text-align: center;">
                            <h3 style="color: white; margin-bottom: 1rem; font-size: 1.8rem;">Law Enforcement Integration</h3>
                            <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 2rem; margin: 1rem 0;">
                                <div style="background: rgba(255,255,255,0.1); padding: 1rem; border-radius: 10px;">
                                    <div style="font-size: 2rem; margin-bottom: 0.5rem;">🏛️</div>
                                    <div style="color: white; font-weight: 600;">Federal</div>
                                </div>
                                <div style="background: rgba(255,255,255,0.1); padding: 1rem; border-radius: 10px;">
                                    <div style="font-size: 2rem; margin-bottom: 0.5rem;">🏢</div>
                                    <div style="color: white; font-weight: 600;">State</div>
                                </div>
                                <div style="background: rgba(255,255,255,0.1); padding: 1rem; border-radius: 10px;">
                                    <div style="font-size: 2rem; margin-bottom: 0.5rem;">🏘️</div>
                                    <div style="color: white; font-weight: 600;">Local</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Slide 16: Property & Assets -->
        <div class="slide" data-slide="16" data-section="data-types">
            <div class="slide-content-wrapper">
                <div class="slide-content">
                    <div class="fade-element">
                        <div class="puzzle-title">Property & Assets</div>
                        <div class="puzzle-subtitle">Real estate, vehicles, and business ownership with asset visualization</div>
                    </div>

                    <div class="fade-element">
                        <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 2rem; margin: 3rem 0;">
                            <div style="background: var(--dark-light); border: 2px solid var(--green); border-radius: 20px; padding: 2rem; text-align: center;">
                                <div style="font-size: 3rem; margin-bottom: 1rem;">🏠</div>
                                <h3 style="color: var(--green); margin-bottom: 1.5rem;">Real Estate</h3>
                                <ul style="list-style: none; padding: 0; text-align: left;">
                                    <li style="padding: 0.5rem 0; color: var(--text-dim);">• Property ownership</li>
                                    <li style="padding: 0.5rem 0; color: var(--text-dim);">• Property values</li>
                                    <li style="padding: 0.5rem 0; color: var(--text-dim);">• Mortgage information</li>
                                    <li style="padding: 0.5rem 0; color: var(--text-dim);">• Tax assessments</li>
                                </ul>
                            </div>

                            <div style="background: var(--dark-light); border: 2px solid var(--primary); border-radius: 20px; padding: 2rem; text-align: center;">
                                <div style="font-size: 3rem; margin-bottom: 1rem;">🚗</div>
                                <h3 style="color: var(--primary); margin-bottom: 1.5rem;">Vehicles</h3>
                                <ul style="list-style: none; padding: 0; text-align: left;">
                                    <li style="padding: 0.5rem 0; color: var(--text-dim);">• Vehicle registrations</li>
                                    <li style="padding: 0.5rem 0; color: var(--text-dim);">• Make, model, year</li>
                                    <li style="padding: 0.5rem 0; color: var(--text-dim);">• License plates</li>
                                    <li style="padding: 0.5rem 0; color: var(--text-dim);">• Insurance records</li>
                                </ul>
                            </div>

                            <div style="background: var(--dark-light); border: 2px solid var(--purple); border-radius: 20px; padding: 2rem; text-align: center;">
                                <div style="font-size: 3rem; margin-bottom: 1rem;">💼</div>
                                <h3 style="color: var(--purple); margin-bottom: 1.5rem;">Business</h3>
                                <ul style="list-style: none; padding: 0; text-align: left;">
                                    <li style="padding: 0.5rem 0; color: var(--text-dim);">• Business affiliations</li>
                                    <li style="padding: 0.5rem 0; color: var(--text-dim);">• Professional licenses</li>
                                    <li style="padding: 0.5rem 0; color: var(--text-dim);">• Corporate records</li>
                                    <li style="padding: 0.5rem 0; color: var(--text-dim);">• Employment history</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <div class="fade-element">
                        <div style="text-align: center; margin: 2rem 0;">
                            <div style="font-size: 1.5rem; color: var(--accent); font-weight: 600; margin-bottom: 2rem;">Asset Visualization Network</div>
                            <div style="display: flex; justify-content: center; align-items: center; gap: 3rem; flex-wrap: wrap;">
                                <div style="position: relative;">
                                    <div style="width: 80px; height: 80px; background: var(--green); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-size: 2rem;">🏠</div>
                                    <div style="position: absolute; top: -10px; right: -10px; background: var(--accent); color: white; border-radius: 50%; width: 30px; height: 30px; display: flex; align-items: center; justify-content: center; font-size: 0.8rem;">$2.1M</div>
                                </div>
                                <div style="width: 3px; height: 40px; background: var(--accent);"></div>
                                <div style="width: 100px; height: 100px; background: var(--gradient-1); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-size: 1.2rem; text-align: center;">Asset<br>Owner</div>
                                <div style="width: 3px; height: 40px; background: var(--accent);"></div>
                                <div style="position: relative;">
                                    <div style="width: 80px; height: 80px; background: var(--primary); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-size: 2rem;">🚗</div>
                                    <div style="position: absolute; top: -10px; right: -10px; background: var(--accent); color: white; border-radius: 50%; width: 30px; height: 30px; display: flex; align-items: center; justify-content: center; font-size: 0.8rem;">3</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Slide 17: Relationship Networks -->
        <div class="slide" data-slide="17" data-section="data-types">
            <div class="slide-content-wrapper">
                <div class="slide-content">
                    <div class="fade-element">
                        <div class="puzzle-title">Relationship Networks</div>
                        <div class="puzzle-subtitle">Family members, associates, and business partners with network visualization</div>
                    </div>

                    <div class="fade-element">
                        <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 3rem; margin: 3rem 0;">
                            <div style="background: var(--dark-light); border-radius: 20px; padding: 2rem;">
                                <h3 style="color: var(--purple); margin-bottom: 2rem; font-size: 1.8rem;">Family Connections</h3>
                                <ul style="list-style: none; padding: 0;">
                                    <li style="padding: 0.8rem 0; border-bottom: 1px solid var(--dark-lighter); display: flex; align-items: center; gap: 1rem;">
                                        <span style="font-size: 1.5rem;">👨‍👩‍👧‍👦</span>
                                        <span style="color: var(--text-dim);">Immediate family</span>
                                    </li>
                                    <li style="padding: 0.8rem 0; border-bottom: 1px solid var(--dark-lighter); display: flex; align-items: center; gap: 1rem;">
                                        <span style="font-size: 1.5rem;">👴</span>
                                        <span style="color: var(--text-dim);">Extended relatives</span>
                                    </li>
                                    <li style="padding: 0.8rem 0; border-bottom: 1px solid var(--dark-lighter); display: flex; align-items: center; gap: 1rem;">
                                        <span style="font-size: 1.5rem;">💍</span>
                                        <span style="color: var(--text-dim);">Spouse/Partners</span>
                                    </li>
                                    <li style="padding: 0.8rem 0; border-bottom: 1px solid var(--dark-lighter); display: flex; align-items: center; gap: 1rem;">
                                        <span style="font-size: 1.5rem;">🏠</span>
                                        <span style="color: var(--text-dim);">Household members</span>
                                    </li>
                                </ul>
                            </div>

                            <div style="background: var(--dark-light); border-radius: 20px; padding: 2rem;">
                                <h3 style="color: var(--accent); margin-bottom: 2rem; font-size: 1.8rem;">Associates & Business</h3>
                                <ul style="list-style: none; padding: 0;">
                                    <li style="padding: 0.8rem 0; border-bottom: 1px solid var(--dark-lighter); display: flex; align-items: center; gap: 1rem;">
                                        <span style="font-size: 1.5rem;">🤝</span>
                                        <span style="color: var(--text-dim);">Business partners</span>
                                    </li>
                                    <li style="padding: 0.8rem 0; border-bottom: 1px solid var(--dark-lighter); display: flex; align-items: center; gap: 1rem;">
                                        <span style="font-size: 1.5rem;">👥</span>
                                        <span style="color: var(--text-dim);">Known associates</span>
                                    </li>
                                    <li style="padding: 0.8rem 0; border-bottom: 1px solid var(--dark-lighter); display: flex; align-items: center; gap: 1rem;">
                                        <span style="font-size: 1.5rem;">📍</span>
                                        <span style="color: var(--text-dim);">Shared addresses</span>
                                    </li>
                                    <li style="padding: 0.8rem 0; border-bottom: 1px solid var(--dark-lighter); display: flex; align-items: center; gap: 1rem;">
                                        <span style="font-size: 1.5rem;">📞</span>
                                        <span style="color: var(--text-dim);">Contact connections</span>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <div class="fade-element">
                        <div style="text-align: center; margin: 2rem 0;">
                            <div style="font-size: 1.5rem; color: var(--secondary); font-weight: 600; margin-bottom: 2rem;">Network Diagram Visualization</div>
                            <div style="position: relative; display: flex; justify-content: center; align-items: center; height: 200px;">
                                <!-- Central node -->
                                <div style="position: absolute; width: 80px; height: 80px; background: var(--gradient-1); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-size: 1.2rem; z-index: 10;">Target</div>

                                <!-- Family nodes -->
                                <div style="position: absolute; top: 0; left: 50%; transform: translateX(-50%); width: 50px; height: 50px; background: var(--purple); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white;">👨‍👩‍👧‍👦</div>
                                <div style="position: absolute; bottom: 0; left: 50%; transform: translateX(-50%); width: 50px; height: 50px; background: var(--purple); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white;">👴</div>

                                <!-- Associate nodes -->
                                <div style="position: absolute; left: 0; top: 50%; transform: translateY(-50%); width: 50px; height: 50px; background: var(--accent); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white;">🤝</div>
                                <div style="position: absolute; right: 0; top: 50%; transform: translateY(-50%); width: 50px; height: 50px; background: var(--accent); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white;">👥</div>

                                <!-- Connection lines -->
                                <div style="position: absolute; width: 2px; height: 60px; background: var(--text-dim); top: 50px; left: 50%; transform: translateX(-50%);"></div>
                                <div style="position: absolute; width: 2px; height: 60px; background: var(--text-dim); bottom: 50px; left: 50%; transform: translateX(-50%);"></div>
                                <div style="position: absolute; width: 60px; height: 2px; background: var(--text-dim); left: 50px; top: 50%; transform: translateY(-50%);"></div>
                                <div style="position: absolute; width: 60px; height: 2px; background: var(--text-dim); right: 50px; top: 50%; transform: translateY(-50%);"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Slide 18: FaceTraceGov Compliance -->
        <div class="slide" data-slide="18" data-section="executive">
            <div class="slide-content-wrapper">
                <div class="slide-content">
                    <div class="fade-element">
                        <div class="puzzle-title">FaceTraceGov Compliance Framework</div>
                        <div class="compliance-grid">
                            <div class="compliance-card fade-element">
                                <h3>Federal Compliance</h3>
                                <ul class="feature-list">
                                    <li>FBI CJIS Security Policy</li>
                                    <li>FedRAMP High Authorization</li>
                                    <li>FIPS 140-2 Level 3 Encryption</li>
                                    <li>Constitutional Privacy Protection</li>
                                </ul>
                            </div>
                            <div class="compliance-card fade-element">
                                <h3>State & Local Standards</h3>
                                <ul class="feature-list">
                                    <li>BIPA Biometric Privacy Act</li>
                                    <li>State-specific Privacy Laws</li>
                                    <li>Municipal Data Ordinances</li>
                                    <li>Cross-jurisdiction Protocols</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Slide 19: The Data Puzzle -->
        <div class="slide" data-slide="19" data-section="puzzle">
            <div class="slide-content-wrapper">
                <div class="slide-content">
                    <div class="fade-element">
                        <div class="puzzle-title">The FaceTraceGov Data Puzzle</div>
                        <div class="puzzle-subtitle">Piecing together data sources for comprehensive suspect identification</div>

                        <div class="puzzle-grid">
                            <div class="puzzle-piece fade-element">
                                <h4>TLO (TransUnion)</h4>
                                <div class="accuracy">96% accuracy</div>
                                <div class="price">$1.80/search</div>
                                <div class="description">Advanced skip tracing with proprietary linking algorithms</div>
                            </div>
                            <div class="puzzle-piece fade-element">
                                <h4>IDI Core</h4>
                                <div class="accuracy">88% accuracy</div>
                                <div class="price">$0.50/search</div>
                                <div class="description">Profiles on every U.S. adult</div>
                            </div>
                            <div class="puzzle-piece fade-element">
                                <h4>Tracers</h4>
                                <div class="accuracy">2,000+ law enforcement</div>
                                <div class="price">98% U.S. coverage</div>
                                <div class="description">#1 trusted cloud-based investigative software</div>
                            </div>
                            <div class="puzzle-piece fade-element">
                                <h4>Clear (Thomson Reuters)</h4>
                                <div class="accuracy">Professional-grade</div>
                                <div class="price">Enterprise licensing</div>
                                <div class="description">Comprehensive public records access</div>
                            </div>
                            <div class="puzzle-piece fade-element">
                                <h4>Accurint (LexisNexis)</h4>
                                <div class="accuracy">Extensive coverage</div>
                                <div class="price">Enterprise security</div>
                                <div class="description">Used by law enforcement nationwide</div>
                            </div>
                            <div class="puzzle-piece fade-element">
                                <h4>IRB Search</h4>
                                <div class="accuracy">85+ billion records</div>
                                <div class="price">$3/search</div>
                                <div class="description">Batch skip tracing functionality</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Slide 20: Architecture Flow -->
        <div class="slide" data-slide="20" data-section="architecture">
            <div class="slide-content-wrapper">
                <div class="slide-content">
                    <div class="fade-element">
                        <div class="puzzle-title">Advanced Architecture</div>
                        <div class="puzzle-subtitle">From grainy footage to positive identification</div>

                        <div class="flow-diagram">
                            <div class="flow-item fade-element">
                                <h4>Input</h4>
                                <p>Security cameras</p>
                            </div>
                            <span class="flow-arrow fade-element">→</span>
                            <div class="flow-item fade-element">
                                <h4>Enhancement</h4>
                                <p>AI upscaling</p>
                            </div>
                            <span class="flow-arrow fade-element">→</span>
                            <div class="flow-item fade-element">
                                <h4>Detection</h4>
                                <p>Face matching</p>
                            </div>
                            <span class="flow-arrow fade-element">→</span>
                            <div class="flow-item fade-element">
                                <h4>Data Fusion</h4>
                                <p>Skip tracing</p>
                            </div>
                            <span class="flow-arrow fade-element">→</span>
                            <div class="flow-item fade-element">
                                <h4>Results</h4>
                                <p>Full profile</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Slide 21: Image Enhancement Technologies -->
        <div class="slide" data-slide="21" data-section="enhancement">
            <div class="slide-content-wrapper">
                <div class="slide-content">
                    <div class="fade-element">
                        <div class="puzzle-title">Image Enhancement Technologies</div>
                        <div class="puzzle-subtitle">Transforming grainy security footage into actionable intelligence</div>

                        <div class="stats-grid">
                            <div class="stat-card fade-element">
                                <span class="stat-number">400%</span>
                                <span class="stat-label">Resolution Increase</span>
                            </div>
                            <div class="stat-card fade-element">
                                <span class="stat-number">85%</span>
                                <span class="stat-label">Success Rate</span>
                            </div>
                            <div class="stat-card fade-element">
                                <span class="stat-number">3s</span>
                                <span class="stat-label">Processing Time</span>
                            </div>
                            <div class="stat-card fade-element">
                                <span class="stat-number">24/7</span>
                                <span class="stat-label">Availability</span>
                            </div>
                        </div>

                        <div class="enhancement-grid">
                            <div class="enhancement-card fade-element">
                                <h3>AI-Powered Enhancement</h3>
                                <ul class="feature-list">
                                    <li>Super-resolution upscaling (CNN/GAN models)</li>
                                    <li>Noise reduction and artifact removal</li>
                                    <li>Motion blur correction algorithms</li>
                                    <li>Low-light enhancement (RetinexNet)</li>
                                    <li>Facial feature reconstruction</li>
                                </ul>
                            </div>
                            <div class="enhancement-card fade-element">
                                <h3>Security Camera Optimization</h3>
                                <ul class="feature-list">
                                    <li>CCTV footage clarification</li>
                                    <li>Body camera stabilization</li>
                                    <li>Traffic camera improvement</li>
                                    <li>ATM camera enhancement</li>
                                    <li>Mobile video processing</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Slide 22: Google Cloud Integration -->
        <div class="slide" data-slide="22" data-section="cloud">
            <div class="slide-content">
                <div class="fade-element">
                    <div class="puzzle-title">Google Cloud Architecture</div>
                    <div class="puzzle-subtitle">Enterprise-grade infrastructure for unlimited scale</div>

                    <div class="cloud-services">
                        <div class="cloud-service fade-element">
                            <h4>Vertex AI</h4>
                            <p>Custom face recognition models with AutoML</p>
                        </div>
                        <div class="cloud-service fade-element">
                            <h4>Cloud TPUs</h4>
                            <p>10-100x faster face matching</p>
                        </div>
                        <div class="cloud-service fade-element">
                            <h4>BigQuery</h4>
                            <p>Cross-reference criminal databases</p>
                        </div>
                        <div class="cloud-service fade-element">
                            <h4>Cloud Spanner</h4>
                            <p>Global data consistency</p>
                        </div>
                        <div class="cloud-service fade-element">
                            <h4>Anthos</h4>
                            <p>Hybrid cloud deployment</p>
                        </div>
                        <div class="cloud-service fade-element">
                            <h4>Cloud KMS</h4>
                            <p>FIPS 140-2 encryption</p>
                        </div>
                    </div>

                    <div class="feature-grid">
                        <div class="feature-card fade-element">
                            <h3>Processing Pipeline</h3>
                            <ul class="feature-list">
                                <li>Cloud Functions for event triggers</li>
                                <li>Dataflow for ETL processing</li>
                                <li>Pub/Sub for real-time messaging</li>
                                <li>Cloud Storage for face datasets</li>
                            </ul>
                        </div>
                        <div class="feature-card fade-element">
                            <h3>Security & Compliance</h3>
                            <ul class="feature-list">
                                <li>VPC Service Controls</li>
                                <li>Identity-Aware Proxy</li>
                                <li>Cloud Audit Logs</li>
                                <li>Data Loss Prevention API</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Slide 23: Architecture Components -->
        <div class="slide" data-slide="23" data-section="architecture">
            <div class="slide-content">
                <div class="fade-element">
                    <div class="puzzle-title">Core Architecture Components</div>
                    <div class="puzzle-subtitle">Operating like advanced search engines</div>
                    
                    <div class="feature-grid">
                        <div class="feature-card fade-element">
                            <h3>Image Processing Pipeline</h3>
                            <ul class="feature-list">
                                <li>Face Detection: CNN models (MTCNN/RetinaFace)</li>
                                <li>Face Alignment: Landmark detection</li>
                                <li>Feature Extraction: FaceNet/ArcFace embeddings</li>
                                <li>Vector Database: Billions of face embeddings</li>
                            </ul>
                        </div>
                        <div class="feature-card fade-element">
                            <h3>Data Storage Architecture</h3>
                            <ul class="feature-list">
                                <li>Hot Storage: Redis for recent searches</li>
                                <li>Warm Storage: Elasticsearch for vectors</li>
                                <li>Cold Storage: Cloud Storage for images</li>
                                <li>Metadata: PostgreSQL for user data</li>
                            </ul>
                        </div>
                        <div class="feature-card fade-element">
                            <h3>Search Infrastructure</h3>
                            <ul class="feature-list">
                                <li>Vector Search: Faiss/Annoy implementation</li>
                                <li>Distributed Processing: Apache Spark</li>
                                <li>Real-time Processing: Kafka streaming</li>
                                <li>API Gateway: Rate limiting & auth</li>
                            </ul>
                        </div>
                        <div class="feature-card fade-element">
                            <h3>Web Crawling System</h3>
                            <ul class="feature-list">
                                <li>Crawler Fleet: Distributed scrapers</li>
                                <li>Image Discovery: CV face detection</li>
                                <li>Deduplication: Perceptual hashing</li>
                                <li>Compliance: Robots.txt & GDPR</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Slide 24: Government Integration -->
        <div class="slide" data-slide="24" data-section="architecture">
            <div class="slide-content">
                <div class="fade-element">
                    <div class="puzzle-title">Government Integration</div>
                    <div class="puzzle-subtitle">Comprehensive coverage at every level</div>

                    <div class="gov-grid">
                        <div class="gov-card fed fade-element">
                            <h3>FaceTraceFed</h3>
                            <p style="margin: 1rem 0;">Federal agency integration:</p>
                            <ul class="feature-list">
                                <li>FBI FACE Services integration</li>
                                <li>Real-time airport/border scanning</li>
                                <li>Interpol database connectivity</li>
                                <li>10M searches/day capacity</li>
                            </ul>
                            <div class="stat-card" style="margin-top: 2rem;">
                                <span class="stat-number" style="font-size: 2rem;">$50M</span>
                                <span class="stat-label">Contract Potential</span>
                            </div>
                        </div>

                        <div class="gov-card state fade-element">
                            <h3>FaceTraceState</h3>
                            <p style="margin: 1rem 0;">State-level law enforcement:</p>
                            <ul class="feature-list">
                                <li>DMV photo database matching</li>
                                <li>Traffic camera integration</li>
                                <li>State prison sync</li>
                                <li>1M searches/day per state</li>
                            </ul>
                            <div class="stat-card" style="margin-top: 2rem;">
                                <span class="stat-number" style="font-size: 2rem;">50</span>
                                <span class="stat-label">States Available</span>
                            </div>
                        </div>

                        <div class="gov-card county fade-element">
                            <h3>FaceTraceCounty</h3>
                            <p style="margin: 1rem 0;">Local law enforcement:</p>
                            <ul class="feature-list">
                                <li>Body camera integration</li>
                                <li>Jail booking photos</li>
                                <li>Municipal cameras</li>
                                <li>Mobile officer app</li>
                            </ul>
                            <div class="stat-card" style="margin-top: 2rem;">
                                <span class="stat-number" style="font-size: 2rem;">3,000+</span>
                                <span class="stat-label">Counties</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Slide 25: Privacy & Compliance -->
        <div class="slide" data-slide="25" data-section="compliance">
            <div class="slide-content">
                <div class="fade-element">
                    <div class="puzzle-title">Privacy & Compliance Framework</div>
                    <div class="puzzle-subtitle">Building trust through comprehensive compliance</div>
                    
                    <div class="compliance-grid">
                        <div class="compliance-card fade-element">
                            <h3>Technical Safeguards</h3>
                            <ul class="feature-list">
                                <li>End-to-end encryption (AES-256)</li>
                                <li>Zero-knowledge architecture options</li>
                                <li>Automatic data expiration (30 days)</li>
                                <li>Blockchain audit trail</li>
                                <li>Differential privacy for analytics</li>
                            </ul>
                        </div>
                        <div class="compliance-card fade-element">
                            <h3>Legal Protections</h3>
                            <ul class="feature-list">
                                <li>$10M cyber liability insurance</li>
                                <li>Legal defense fund</li>
                                <li>Regular third-party audits</li>
                                <li>Clear law enforcement guidelines</li>
                                <li>Warrant requirement system</li>
                            </ul>
                        </div>
                        <div class="compliance-card fade-element">
                            <h3>Ethical AI Implementation</h3>
                            <ul class="feature-list">
                                <li>Bias mitigation algorithms</li>
                                <li>Diverse training datasets</li>
                                <li>Explainable AI for matches</li>
                                <li>Human review requirements</li>
                                <li>Transparency reports</li>
                            </ul>
                        </div>
                        <div class="compliance-card fade-element">
                            <h3>Data Governance</h3>
                            <ul class="feature-list">
                                <li>FCRA compliance for skip tracing</li>
                                <li>GDPR/CCPA data rights</li>
                                <li>Consent management system</li>
                                <li>Right to deletion</li>
                                <li>Data minimization practices</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Slide 26: Technical Implementation - BigQuery Schema -->
        <div class="slide" data-slide="26" data-section="architecture">
            <div class="slide-content-wrapper">
                <div class="slide-content">
                    <div class="fade-element">
                        <div class="puzzle-title">BigQuery Schema Examples</div>
                        <div class="puzzle-subtitle">Dataset hierarchy structure and data pipeline integration</div>
                    </div>

                    <div class="fade-element">
                        <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 2rem; margin: 2rem 0;">
                            <div style="background: var(--dark-light); border-radius: 15px; padding: 1.5rem;">
                                <h3 style="color: var(--primary); margin-bottom: 1rem;">IDI Core Schema</h3>
                                <pre style="background: var(--dark-lighter); padding: 1rem; border-radius: 8px; color: var(--text-dim); font-size: 0.8rem; overflow-x: auto;">
CREATE TABLE `facetrace.idi_core.people` (
  person_id STRING,
  full_name STRING,
  date_of_birth DATE,
  ssn STRING,
  addresses ARRAY&lt;STRUCT&lt;
    address STRING,
    city STRING,
    state STRING,
    zip_code STRING,
    date_range STRUCT&lt;
      start_date DATE,
      end_date DATE
    &gt;
  &gt;&gt;,
  phones ARRAY&lt;STRING&gt;,
  emails ARRAY&lt;STRING&gt;
);
                                </pre>
                            </div>

                            <div style="background: var(--dark-light); border-radius: 15px; padding: 1.5rem;">
                                <h3 style="color: var(--accent); margin-bottom: 1rem;">Face Embeddings</h3>
                                <pre style="background: var(--dark-lighter); padding: 1rem; border-radius: 8px; color: var(--text-dim); font-size: 0.8rem; overflow-x: auto;">
CREATE TABLE `facetrace.ml.face_embeddings` (
  embedding_id STRING,
  person_id STRING,
  image_url STRING,
  embedding ARRAY&lt;FLOAT64&gt;,
  confidence_score FLOAT64,
  source_platform STRING,
  created_timestamp TIMESTAMP,
  face_landmarks JSON
);
                                </pre>
                            </div>
                        </div>
                    </div>

                    <div class="fade-element">
                        <div style="text-align: center; margin: 2rem 0;">
                            <div style="font-size: 1.5rem; color: var(--secondary); font-weight: 600; margin-bottom: 1rem;">Data Pipeline Workflow</div>
                            <div style="display: flex; justify-content: center; align-items: center; gap: 2rem; flex-wrap: wrap;">
                                <div style="background: var(--primary); padding: 1rem; border-radius: 10px; color: white; min-width: 120px; text-align: center;">Raw Data<br>Ingestion</div>
                                <div style="font-size: 2rem; color: var(--primary);">→</div>
                                <div style="background: var(--accent); padding: 1rem; border-radius: 10px; color: white; min-width: 120px; text-align: center;">Schema<br>Validation</div>
                                <div style="font-size: 2rem; color: var(--primary);">→</div>
                                <div style="background: var(--secondary); padding: 1rem; border-radius: 10px; color: white; min-width: 120px; text-align: center;">BigQuery<br>Storage</div>
                                <div style="font-size: 2rem; color: var(--primary);">→</div>
                                <div style="background: var(--green); padding: 1rem; border-radius: 10px; color: white; min-width: 120px; text-align: center;">ML Model<br>Training</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Slide 27: Privacy & Compliance Framework -->
        <div class="slide" data-slide="27" data-section="compliance">
            <div class="slide-content-wrapper">
                <div class="slide-content">
                    <div class="fade-element">
                        <div class="puzzle-title">Privacy & Compliance Framework</div>
                        <div class="puzzle-subtitle">Building trust through comprehensive compliance and ethical AI</div>
                    </div>

                    <div class="fade-element">
                        <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 2rem; margin: 2rem 0;">
                            <div style="background: var(--dark-light); border: 2px solid var(--primary); border-radius: 20px; padding: 2rem;">
                                <h3 style="color: var(--primary); margin-bottom: 1.5rem;">Technical Safeguards</h3>
                                <ul style="list-style: none; padding: 0;">
                                    <li style="padding: 0.5rem 0; color: var(--text-dim);">🔒 End-to-end encryption (AES-256)</li>
                                    <li style="padding: 0.5rem 0; color: var(--text-dim);">🔐 Zero-knowledge architecture options</li>
                                    <li style="padding: 0.5rem 0; color: var(--text-dim);">⏰ Automatic data expiration (30 days)</li>
                                    <li style="padding: 0.5rem 0; color: var(--text-dim);">⛓️ Blockchain audit trail</li>
                                    <li style="padding: 0.5rem 0; color: var(--text-dim);">📊 Differential privacy for analytics</li>
                                </ul>
                            </div>

                            <div style="background: var(--dark-light); border: 2px solid var(--accent); border-radius: 20px; padding: 2rem;">
                                <h3 style="color: var(--accent); margin-bottom: 1.5rem;">Legal Protections</h3>
                                <ul style="list-style: none; padding: 0;">
                                    <li style="padding: 0.5rem 0; color: var(--text-dim);">💰 $10M cyber liability insurance</li>
                                    <li style="padding: 0.5rem 0; color: var(--text-dim);">⚖️ Legal defense fund</li>
                                    <li style="padding: 0.5rem 0; color: var(--text-dim);">🔍 Regular third-party audits</li>
                                    <li style="padding: 0.5rem 0; color: var(--text-dim);">📋 Clear law enforcement guidelines</li>
                                    <li style="padding: 0.5rem 0; color: var(--text-dim);">📜 Warrant requirement system</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <div class="fade-element">
                        <div style="background: var(--gradient-1); border-radius: 20px; padding: 2rem; margin: 2rem 0; text-align: center;">
                            <h3 style="color: white; margin-bottom: 1rem;">Ethical AI Implementation</h3>
                            <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 1rem;">
                                <div style="background: rgba(255,255,255,0.1); padding: 1rem; border-radius: 10px;">
                                    <div style="font-size: 2rem; margin-bottom: 0.5rem;">⚖️</div>
                                    <div style="color: white; font-size: 0.9rem;">Bias Mitigation</div>
                                </div>
                                <div style="background: rgba(255,255,255,0.1); padding: 1rem; border-radius: 10px;">
                                    <div style="font-size: 2rem; margin-bottom: 0.5rem;">🔍</div>
                                    <div style="color: white; font-size: 0.9rem;">Explainable AI</div>
                                </div>
                                <div style="background: rgba(255,255,255,0.1); padding: 1rem; border-radius: 10px;">
                                    <div style="font-size: 2rem; margin-bottom: 0.5rem;">👥</div>
                                    <div style="color: white; font-size: 0.9rem;">Human Review</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Slide 28: Call to Action -->
        <div class="slide" data-slide="28" data-section="conclusion">
            <div class="slide-content">
                <div class="fade-element">
                    <div class="cta-container">
                        <h3>Ready to Scale?</h3>
                        <p>FaceTrace is positioned to revolutionize suspect identification across America</p>
                        
                        <div class="stats-grid" style="margin: 3rem 0;">
                            <div class="stat-card" style="background: rgba(255,255,255,0.1); border: 2px solid rgba(255,255,255,0.3);">
                                <span class="stat-number" style="color: white;">75%</span>
                                <span class="stat-label" style="color: rgba(255,255,255,0.8);">Time Reduction</span>
                            </div>
                            <div class="stat-card" style="background: rgba(255,255,255,0.1); border: 2px solid rgba(255,255,255,0.3);">
                                <span class="stat-number" style="color: white;">96%</span>
                                <span class="stat-label" style="color: rgba(255,255,255,0.8);">Accuracy Rate</span>
                            </div>
                            <div class="stat-card" style="background: rgba(255,255,255,0.1); border: 2px solid rgba(255,255,255,0.3);">
                                <span class="stat-number" style="color: white;">$26M</span>
                                <span class="stat-label" style="color: rgba(255,255,255,0.8);">Annual Revenue</span>
                            </div>
                            <div class="stat-card" style="background: rgba(255,255,255,0.1); border: 2px solid rgba(255,255,255,0.3);">
                                <span class="stat-number" style="color: white;">940%</span>
                                <span class="stat-label" style="color: rgba(255,255,255,0.8);">ROI</span>
                            </div>
                        </div>
                        
                        <button class="cta-button">Begin Implementation</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Controls Hint -->
    <div class="controls-hint">
        Press SPACE to advance • Press ← → to navigate
    </div>

    <script>
        // Presentation state
        let currentSlide = 1;
        let currentElement = 0;
        const totalSlides = 28;
        let countdownInterval = null;
        let countdownValue = 5;

        // Update progress and counter
        function updateProgress() {
            const progress = (currentSlide / totalSlides) * 100;
            document.getElementById('progressBar').style.width = progress + '%';
            document.getElementById('currentSlide').textContent = currentSlide;
            
            // Update navigation
            updateNavigation();
        }

        // Update navigation bar
        function updateNavigation() {
            const activeSlide = document.querySelector('.slide.active');
            const section = activeSlide.dataset.section;
            
            document.querySelectorAll('.nav-item').forEach(item => {
                item.classList.remove('active');
                if (item.dataset.section === section) {
                    item.classList.add('active');
                }
            });
            
            // Always show nav bar
            const navBar = document.getElementById('navBar');
            if (navBar) {
                navBar.style.display = 'flex';
            }
        }

        // Show specific slide
        function showSlide(slideNumber) {
            // Hide all slides
            document.querySelectorAll('.slide').forEach(slide => {
                slide.classList.remove('active');
            });

            // Show target slide
            const targetSlide = document.querySelector(`[data-slide="${slideNumber}"]`);
            if (targetSlide) {
                targetSlide.classList.add('active');

                // Reset fade elements first
                const fadeElements = targetSlide.querySelectorAll('.fade-element');
                fadeElements.forEach(element => {
                    element.classList.remove('visible');
                });

                // Show ALL fade elements immediately for this slide
                setTimeout(() => {
                    fadeElements.forEach((element, index) => {
                        // Add a small delay for staggered animation effect
                        setTimeout(() => {
                            element.classList.add('visible');
                        }, index * 100);
                    });
                }, 50);

                currentElement = fadeElements.length - 1; // Set to last element
                updateProgress();

                // Handle countdown only if countdown element exists
                const countdownEl = document.getElementById('countdown');
                if (slideNumber === 5 && countdownEl) {
                    startCountdown();
                }
            }
        }

        // Start countdown
        function startCountdown() {
            countdownValue = 5;
            const countdownEl = document.getElementById('countdown');
            
            countdownInterval = setInterval(() => {
                countdownValue--;
                countdownEl.textContent = countdownValue;
                
                if (countdownValue <= 0) {
                    clearInterval(countdownInterval);
                    setTimeout(() => {
                        currentSlide++;
                        showSlide(currentSlide);
                    }, 500);
                }
            }, 1000);
        }

        // Show next element or slide
        function nextElement() {
            if (currentSlide < totalSlides) {
                // Move to next slide (all elements will be shown automatically)
                currentSlide++;
                showSlide(currentSlide);
            }
        }

        // Show previous slide
        function previousSlide() {
            if (currentSlide > 1) {
                // Clear countdown if active
                if (countdownInterval) {
                    clearInterval(countdownInterval);
                }
                currentSlide--;
                showSlide(currentSlide);
            }
        }

        // Keyboard controls
        document.addEventListener('keydown', (e) => {
            if (e.code === 'Space') {
                e.preventDefault();
                nextElement();
            } else if (e.code === 'ArrowRight') {
                e.preventDefault();
                if (currentSlide < totalSlides) {
                    if (countdownInterval) {
                        clearInterval(countdownInterval);
                    }
                    currentSlide++;
                    showSlide(currentSlide);
                }
            } else if (e.code === 'ArrowLeft') {
                e.preventDefault();
                previousSlide();
            }
        });

        // Navigation click handlers
        document.querySelectorAll('.nav-item').forEach(item => {
            item.addEventListener('click', () => {
                const section = item.dataset.section;
                // Find the first slide with this section
                const slideWithSection = document.querySelector(`.slide[data-section="${section}"]`);
                if (slideWithSection) {
                    const slideNumber = parseInt(slideWithSection.dataset.slide);
                    if (slideNumber && slideNumber !== currentSlide) {
                        currentSlide = slideNumber;
                        showSlide(currentSlide);
                    }
                }
            });
        });

        // Initialize
        updateProgress();
        document.getElementById('totalSlides').textContent = totalSlides;

        // Mouse click to advance
        document.addEventListener('click', (e) => {
            // Don't advance on nav items, buttons, or other interactive elements
            const isInteractive = e.target.classList.contains('nav-item') || 
                                e.target.tagName === 'BUTTON' || 
                                e.target.closest('.nav-bar') ||
                                e.target.closest('.progress-container');
            
            if (!isInteractive) {
                nextElement();
            }
        });
        // Typewriter Effect for Slide 1
        function initTypewriter() {
            const text = "Who's Behind The Face?";
            const typewriterElement = document.getElementById('typewriter-text');
            const cursorElement = document.getElementById('typewriter-cursor');

            if (!typewriterElement) return;

            let index = 0;
            const typingSpeed = 3000 / text.length; // 3 seconds total

            function typeCharacter() {
                if (index < text.length) {
                    typewriterElement.textContent += text.charAt(index);
                    index++;
                    setTimeout(typeCharacter, typingSpeed);
                } else {
                    // Show cursor after typing is complete
                    if (cursorElement) {
                        cursorElement.style.display = 'inline-block';
                    }
                }
            }

            // Start typing after a brief delay
            setTimeout(() => {
                typewriterElement.textContent = '';
                if (cursorElement) {
                    cursorElement.style.display = 'none';
                }
                typeCharacter();
            }, 500);
        }

        // Initialize typewriter when slide 1 is active
        document.addEventListener('DOMContentLoaded', function() {
            if (currentSlide === 1) {
                initTypewriter();
            }
        });

        // Re-initialize typewriter when returning to slide 1
        const originalShowSlide = showSlide;
        showSlide = function(slideNumber) {
            originalShowSlide(slideNumber);
            if (slideNumber === 1) {
                setTimeout(initTypewriter, 100);
            }
        };

    </script>
</body>
</html>