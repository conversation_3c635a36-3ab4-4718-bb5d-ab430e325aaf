#!/bin/bash

# FaceTrace Backend API Monitoring and Logging Setup
# Configures Cloud Monitoring, Logging, and Alerting for the backend API

set -e

PROJECT_ID="gold-braid-458901-v2"
SERVICE_NAME="facetrace-backend-api"
REGION="us-central1"

echo "📊 Setting up monitoring and logging for FaceTrace Backend API..."

# Enable required APIs
echo "🔧 Enabling required Google Cloud APIs..."
gcloud services enable monitoring.googleapis.com
gcloud services enable logging.googleapis.com
gcloud services enable clouderrorreporting.googleapis.com
gcloud services enable cloudtrace.googleapis.com

# Create log-based metrics
echo "📈 Creating log-based metrics..."

# API request count metric
gcloud logging metrics create api_request_count \
  --description="Count of API requests" \
  --log-filter='resource.type="cloud_run_revision" AND resource.labels.service_name="'$SERVICE_NAME'" AND httpRequest.requestMethod!=""' \
  --project=$PROJECT_ID || echo "Metric api_request_count already exists"

# Error rate metric
gcloud logging metrics create api_error_rate \
  --description="Rate of API errors (4xx and 5xx responses)" \
  --log-filter='resource.type="cloud_run_revision" AND resource.labels.service_name="'$SERVICE_NAME'" AND httpRequest.status>=400' \
  --project=$PROJECT_ID || echo "Metric api_error_rate already exists"

# Response time metric
gcloud logging metrics create api_response_time \
  --description="API response time" \
  --log-filter='resource.type="cloud_run_revision" AND resource.labels.service_name="'$SERVICE_NAME'" AND httpRequest.requestMethod!=""' \
  --project=$PROJECT_ID || echo "Metric api_response_time already exists"

echo "🚨 Setting up alerting policies..."

# Create notification channel (email)
echo "📧 To set up email notifications, run:"
echo "gcloud alpha monitoring channels create --display-name='FaceTrace Alerts' --type=email --channel-labels=email_address=<EMAIL>"

echo "📊 Setting up dashboard..."
cat > monitoring-dashboard.json << 'EOF'
{
  "displayName": "FaceTrace Backend API Dashboard",
  "mosaicLayout": {
    "tiles": [
      {
        "width": 6,
        "height": 4,
        "widget": {
          "title": "Request Rate",
          "xyChart": {
            "dataSets": [
              {
                "timeSeriesQuery": {
                  "timeSeriesFilter": {
                    "filter": "resource.type=\"cloud_run_revision\" AND resource.label.service_name=\"facetrace-backend-api\"",
                    "aggregation": {
                      "alignmentPeriod": "60s",
                      "perSeriesAligner": "ALIGN_RATE"
                    }
                  }
                }
              }
            ]
          }
        }
      }
    ]
  }
}
EOF

echo "✅ Monitoring setup completed!"
echo ""
echo "📊 Monitoring Resources Created:"
echo "- Log-based metrics for request count, error rate, and response time"
echo "- Dashboard configuration (monitoring-dashboard.json)"
echo ""
echo "🔗 Access your monitoring:"
echo "- Logs: https://console.cloud.google.com/logs/query?project=$PROJECT_ID"
echo "- Monitoring: https://console.cloud.google.com/monitoring?project=$PROJECT_ID"
echo "- Cloud Run: https://console.cloud.google.com/run?project=$PROJECT_ID"
echo ""
echo "📝 Manual steps required:"
echo "1. Set up email notification channel for alerts"
echo "2. Import the dashboard configuration"
echo "3. Configure custom alerting policies as needed"
